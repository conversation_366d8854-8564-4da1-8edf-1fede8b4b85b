import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/auth_state_manager.dart';
import '../services/auth_service.dart';
import '../services/itinerary_service.dart';
import '../services/collaborative_itinerary_service.dart';
import '../models/itinerary.dart';
import '../models/collaborative_itinerary.dart';
import '../utils/navigation_helper.dart';
import '../generated/l10n/app_localizations.dart';
import 'debug_google_signin_screen.dart';
import 'match_tab_screen.dart';
import 'create_itinerary_screen.dart';
import 'itinerary_detail_screen.dart';
import 'collaborative_itinerary_detail_screen.dart';
import 'wanderly_chat_screen.dart';
// plan_page.dart import removed - now using itinerary list instead
import 'profile_screen.dart';

class HomepageScreen extends StatefulWidget {
  const HomepageScreen({super.key});

  @override
  State<HomepageScreen> createState() => _HomepageScreenState();
}

class _HomepageScreenState extends State<HomepageScreen>
    with TickerProviderStateMixin {
  int _currentIndex = 2;
  late AnimationController _dotAnimationController;
  late AnimationController _pageAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isLoggingOut = false;

  // Remove TabController since we're eliminating the TabBar

  // Itinerary data
  List<Itinerary> _itineraries = [];
  bool _isLoadingItineraries = false;

  // Collaborative itinerary data
  List<CollaborativeItinerary> _collaborativeItineraries = [];
  bool _isLoadingCollaborativeItineraries = false;

  // Persistent page widgets to maintain state
  late Widget _chatPage;
  late Widget _matchPage;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _dotAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200), // Reduced from 400ms
      vsync: this,
    );

    // TabController removed - no longer needed

    // Initialize simplified animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
          parent: _pageAnimationController,
          curve: Curves.easeInOut), // Simplified curve
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.1), // Reduced from 0.3 for lighter effect
      end: Offset.zero,
    ).animate(CurvedAnimation(
        parent: _pageAnimationController,
        curve: Curves.easeInOut)); // Simplified curve

    // Start initial animation
    _pageAnimationController.forward();

    // Initialize persistent page widgets
    _initializePages();

    // Load itineraries asynchronously to prevent blocking UI
    _loadItinerariesAsync();
  }

  /// Load itineraries asynchronously to prevent UI blocking
  Future<void> _loadItinerariesAsync() async {
    try {
      // First, clean up any placeholder itineraries
      await ItineraryService.removePlaceholderItineraries().catchError((error) {
        if (kDebugMode) {
          print('Error removing placeholder itineraries: $error');
        }
        return false;
      });

      // Load both types of itineraries concurrently but don't block UI
      _loadItineraries().catchError((error) {
        if (kDebugMode) {
          print('Error loading itineraries: $error');
        }
      });

      _loadCollaborativeItineraries().catchError((error) {
        if (kDebugMode) {
          print('Error loading collaborative itineraries: $error');
        }
      });
    } catch (error) {
      if (kDebugMode) {
        print('Itinerary loading error: $error');
      }
      // Don't rethrow - we want the UI to continue working even if loading fails
    }
  }

  @override
  void dispose() {
    _dotAnimationController.dispose();
    _pageAnimationController.dispose();
    super.dispose();
  }

  void _initializePages() {
    // Initialize all persistent pages to maintain state
    _chatPage = WanderlyChatScreen(
      key: GlobalKey(),
      onNavigateToTab: _onNavItemTapped,
    );
    _matchPage = const MatchTabScreen();
    // Note: _homePage, _itineraryPage, and _profilePage are built dynamically
    // because they depend on state that changes (like itinerary data)
  }

  void _onNavItemTapped(int index) async {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });

      // Simple page animation without complex transitions
      _pageAnimationController.reset();
      _pageAnimationController.forward();
    }
  }

  // _buildHomePage removed - replaced with Wanderly Chat as index 0

  // Old _buildItineraryPage removed - replaced with separate _buildPlanPage and _buildCollaborativePage

  Widget _buildPlanPage() {
    return Stack(
      children: [
        // Main content - Itinerary list instead of single itinerary map
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Itinerary list content with bottom padding for fixed button
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildItineraryList(),
                ),
              ),
              // Add bottom padding to prevent content from being hidden behind the button
              const SizedBox(height: 80),
            ],
          ),
        ),

        // Fixed Create Itinerary Button
        Positioned(
          left: 16,
          right: 16,
          bottom: 16,
          child: _buildCreateItineraryButton(),
        ),
      ],
    );
  }

  Widget _buildItineraryList() {
    if (_isLoadingItineraries) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
        ),
      );
    }

    if (_itineraries.isEmpty) {
      return _buildEmptyItineraryState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Itinerary List Header
          Text(
            AppLocalizations.of(context).yourItineraries,
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 16),

          // Real itinerary cards
          ..._itineraries.map((itinerary) => Column(
                children: [
                  GestureDetector(
                    onTap: () => _navigateToItineraryDetail(itinerary),
                    child: _buildItineraryCard(
                      title: itinerary.title,
                      dateRange: itinerary.dateRange,
                      spots: itinerary.destinationCount,
                      itinerary: itinerary,
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              )),
        ],
      ),
    );
  }

  Widget _buildEmptyItineraryState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noItineraryFound,
            style: GoogleFonts.instrumentSans(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).askAiToCreateTravelPlan,
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF718096),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCollaborativePage() {
    return Stack(
      children: [
        // Main content - Collaborative itinerary as standalone page
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Collaborative content with bottom padding for fixed button
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildCollabTab(),
                ),
              ),
              // Add bottom padding to prevent content from being hidden behind the button
              const SizedBox(height: 80),
            ],
          ),
        ),

        // Fixed Create Itinerary Button (for collaborative itineraries)
        Positioned(
          left: 16,
          right: 16,
          bottom: 16,
          child: _buildCreateItineraryButton(),
        ),
      ],
    );
  }

  Widget _buildProfilePage() {
    return const ProfileScreen();
  }

  // TabBar removed - no longer needed since we have separate navigation tabs

  // _buildPlanTabWithoutButton removed - functionality moved to _buildItineraryList in _buildPlanPage

  Widget _buildItineraryImage(Itinerary itinerary) {
    // Check if itinerary has a custom cover photo
    if (itinerary.hasPhoto && itinerary.imagePath != null) {
      final imageFile = File(itinerary.imagePath!);

      // Check if the file exists
      if (imageFile.existsSync()) {
        return Image.file(
          imageFile,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // If local image fails, fall back to default destination image
            return _buildDefaultDestinationImage(itinerary.primaryDestination);
          },
        );
      }
    }

    // Fall back to default destination image
    return _buildDefaultDestinationImage(itinerary.primaryDestination);
  }

  Widget _buildDefaultDestinationImage(String destination) {
    final imageUrl = _getImageUrlForDestination(destination);
    return Image.network(
      imageUrl,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: const Color(0xFF0D76FF).withOpacity(0.1),
          child: const Icon(
            Icons.image,
            color: Color(0xFF0D76FF),
            size: 48,
          ),
        );
      },
    );
  }

  String _getImageUrlForDestination(String destination) {
    // Map destinations to appropriate images
    final destinationImages = {
      'bali':
          'https://plus.unsplash.com/premium_photo-1661878915254-f3163e91d870?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YmFsaXxlbnwwfHwwfHx8MA%3D%3D',
      'tokyo':
          'https://plus.unsplash.com/premium_photo-1661914240950-b0124f20a5c1?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8dG9reW98ZW58MHx8MHx8fDA%3D',
      'paris':
          'https://images.unsplash.com/photo-1431274172761-fca41d930114?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8cGFyaXN8ZW58MHwwfDB8fHww',
      'london':
          'https://images.unsplash.com/photo-1486299267070-83823f5448dd?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8bG9uZG9ufGVufDB8MHwwfHx8MA%3D%3D',
      'new york':
          'https://images.unsplash.com/photo-1499092346589-b9b6be3e94b2?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fG5ldyUyMHlvcmt8ZW58MHwwfDB8fHww',
      'thailand':
          'https://images.unsplash.com/photo-1503933166348-a1a86c17b3a0?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fHRoYWlsYW5kfGVufDB8MHwwfHx8MA%3D%3D',
      'italy':
          'https://images.unsplash.com/photo-1514890547357-a9ee288728e0?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fGl0YWx5fGVufDB8MHwwfHx8MA%3D%3D',
      'spain':
          'https://images.unsplash.com/photo-1539037116277-4db20889f2d4?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fHNwYWlufGVufDB8MHwwfHx8MA%3D%3D',
      'greece':
          'https://images.unsplash.com/photo-1594048069339-42ae0e89376a?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8ODJ8fGdyZWVjZXxlbnwwfDB8MHx8fDA%3D',
      'iceland':
          'https://images.unsplash.com/photo-1529963183134-61a90db47eaf?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8aWNlbGFuZHxlbnwwfDB8MHx8fDA%3D',
      'dubai':
          'https://images.unsplash.com/photo-1580674684081-7617fbf3d745?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8ZHViYWl8ZW58MHwwfDB8fHww',
      'singapore':
          'https://images.unsplash.com/photo-1600664356348-10686526af4f?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8c2luZ2Fwb3JlfGVufDB8MHwwfHx8MA%3D%3D',
      'sydney':
          'https://images.unsplash.com/photo-1523428096881-5bd79d043006?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8c3lkbmV5fGVufDB8MHwwfHx8MA%3D%3D',
      'seoul':
          'https://images.unsplash.com/photo-1597552571860-136a103d5eb3?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'amsterdam':
          'https://images.unsplash.com/photo-1576924542622-772281b13aa8?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fGFtc3RlcmRhbXxlbnwwfDB8MHx8fDA%3D',
      'berlin':
          'https://images.unsplash.com/photo-1560969184-10fe8719e047?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'moscow':
          'https://images.unsplash.com/photo-1749971496139-cbde7fb92e57?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8J21vc2NvdyclM0F8ZW58MHwwfDB8fHww',
      'cape town':
          'https://images.unsplash.com/photo-1591742708307-ce49d19450d4?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fGNhcGUlMjB0b3dufGVufDB8MHwwfHx8MA%3D%3D',
      'cairo':
          'https://images.unsplash.com/photo-1572252009286-268acec5ca0a?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y2Fpcm98ZW58MHwwfDB8fHww',
      'los angeles':
          'https://images.unsplash.com/photo-1429554429301-1c7d5ae2d42e?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGxvcyUyMGFuZ2VsZXN8ZW58MHwwfDB8fHww',
      'rio de janeiro':
          'https://images.unsplash.com/photo-1700677866571-43199bcbc593?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NzB8fHJpbyUyMGRlJTIwamFuZWlyb3xlbnwwfDB8MHx8fDA%3D',
      'rome':
          'https://images.unsplash.com/photo-1552832230-c0197dd311b5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8cm9tZXxlbnwwfDB8MHx8fDA%3D',
      'barcelona':
          'https://images.unsplash.com/photo-1630219694734-fe47ab76b15e?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8YmFyY2Vsb25hfGVufDB8MHwwfHx8MA%3D%3D',
      'istanbul':
          'https://images.unsplash.com/photo-1589561454226-796a8aa89b05?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8aXN0YW5idWx8ZW58MHwwfDB8fHww',
      'prague':
          'https://images.unsplash.com/photo-1564511287568-54483b52a35e?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8cHJhZ3VlfGVufDB8MHwwfHx8MA%3D%3D',
      'vienna':
          'https://images.unsplash.com/photo-1519923041107-e4dc8d9193da?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8dmllbm5hfGVufDB8MHwwfHx8MA%3D%3D',
      'venice':
          'https://plus.unsplash.com/premium_photo-1661963047742-dabc5a735357?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8dmVuaWNlfGVufDB8MHwwfHx8MA%3D%3D',
      'las vegas':
          'https://images.unsplash.com/photo-1587223043646-fa771d9e0c8b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bGFzJTIwdmVnYXN8ZW58MHwwfDB8fHww',
      'mexico':
          'https://images.unsplash.com/photo-1585464231875-d9ef1f5ad396?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8bWV4aWNvJTIwY2l0eXxlbnwwfDB8MHx8fDA%3D',
      'maldives':
          'https://images.unsplash.com/photo-1590523277543-a94d2e4eb00b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8bWFsZGl2ZXN8ZW58MHwwfDB8fHww',
    };

    // Try to find a matching image based on destination name
    final lowerDestination = destination.toLowerCase();
    for (final key in destinationImages.keys) {
      if (lowerDestination.contains(key)) {
        return destinationImages[key]!;
      }
    }

    // Default travel image if no match found
    return 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800';
  }

  Widget _buildCollabTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Text(
            'Collaborative Itineraries',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Join shared itineraries or collaborate with others',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              height: 1.4,
            ),
          ),
          const SizedBox(height: 24),

          // Join Itinerary Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.group_add,
                    size: 30,
                    color: Color(0xFF0D76FF),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Join Itinerary',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Enter a 6-digit code to join a shared itinerary',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    color: const Color(0xFF718096),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _showJoinItineraryDialog,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0D76FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Join with Code',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Collaborative Itineraries List
          if (_collaborativeItineraries.isNotEmpty) ...[
            Text(
              'Your Collaborative Itineraries',
              style: GoogleFonts.instrumentSans(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2D3748),
              ),
            ),
            const SizedBox(height: 16),
            ..._collaborativeItineraries.map((itinerary) => Column(
                  children: [
                    _buildCollaborativeItineraryCard(itinerary),
                    const SizedBox(height: 16),
                  ],
                )),
          ] else ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F9FC),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: const Color(0xFFE2E8F0),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 48,
                    color: const Color(0xFF718096).withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Collaborative Itineraries',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF718096),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Join an itinerary with a code or share your own',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      color: const Color(0xFF718096),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildItineraryCard({
    required String title,
    required String dateRange,
    required int spots,
    required Itinerary itinerary,
  }) {
    return Container(
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Background Image
            Positioned.fill(
              child: _buildItineraryImage(itinerary),
            ),

            // Gradient Overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
              ),
            ),

            // Content
            Positioned(
              left: 20,
              bottom: 20,
              right: 20,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 14,
                        color: Colors.white.withOpacity(0.8),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        dateRange,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Total',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 12,
                              color: Colors.white.withOpacity(0.8),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '$spots Spots',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      // Small map placeholder
                      Container(
                        width: 60,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.map,
                          color: Colors.white.withOpacity(0.8),
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateItineraryButton() {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        border: Border.all(
          color: const Color(0xFF0D76FF),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _navigateToCreateItinerary,
          borderRadius: BorderRadius.circular(16),
          child: Center(
            child: Text(
              'Create Itinerary',
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF0D76FF),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderPage({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(48),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 80,
                  color: const Color(0xFF0D76FF).withOpacity(0.3),
                ),
                const SizedBox(height: 24),
                Text(
                  title,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2D3748),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  description,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: const Color(0xFF718096),
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Coming Soon',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF0D76FF),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadItineraries() async {
    setState(() {
      _isLoadingItineraries = true;
    });

    try {
      final itineraries = await ItineraryService.getAllItineraries();
      if (kDebugMode) {
        print('Loaded ${itineraries.length} itineraries:');
        for (int i = 0; i < itineraries.length; i++) {
          print('  $i: ${itineraries[i].title} (${itineraries[i].id})');
        }
      }
      setState(() {
        _itineraries = itineraries;
        _isLoadingItineraries = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingItineraries = false;
      });
      if (kDebugMode) {
        print('Error loading itineraries: $e');
      }
    }
  }

  void _navigateToCreateItinerary() async {
    final result = await Navigator.of(context).push(
      NavigationHelper.createSlideRoute(
        const CreateItineraryScreen(),
      ),
    );

    // Refresh itineraries if a new one was created
    if (result == true) {
      _loadItineraries();
    }
  }

  void _navigateToItineraryDetail(Itinerary itinerary) async {
    final result = await Navigator.of(context).push(
      NavigationHelper.createSlideRoute(
        ItineraryDetailScreen(itinerary: itinerary),
      ),
    );

    // Refresh itineraries if any changes were made (like deletion)
    if (result == true) {
      _loadItineraries();
    }
  }

  void _handleLogout() async {
    setState(() {
      _isLoggingOut = true;
    });

    try {
      // Check if user is a guest before signing out
      final isGuest = AuthService.isGuestUser;

      if (mounted && kDebugMode) {
        print('Homepage: Logging out user (isGuest: $isGuest)');
      }

      // Sign out the user
      await AuthStateManager().signOut();

      // Handle navigation based on user type
      if (mounted) {
        NavigationHelper.handleLogoutNavigation(context, isGuest: isGuest);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).logoutFailed(error.toString()),
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );

        setState(() {
          _isLoggingOut = false;
        });
      }
    }
  }

  void _showLogoutDialog() {
    final isGuest = AuthService.isGuestUser;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            isGuest ? 'End Guest Session' : 'Logout',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Text(
            isGuest
                ? 'Are you sure you want to end your guest session? You\'ll return to the login screen.'
                : 'Are you sure you want to logout?',
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF718096),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                AppLocalizations.of(context).cancel,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _handleLogout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                isGuest
                    ? AppLocalizations.of(context).endSession
                    : AppLocalizations.of(context).logout,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  final iconList = <IconData>[
    Icons.brightness_5,
    Icons.brightness_4,
    Icons.brightness_6,
    Icons.brightness_7,
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      appBar: _currentIndex == 0 ||
              _currentIndex == 1 ||
              _currentIndex == 3 ||
              _currentIndex == 4
          ? null
          : AppBar(
              backgroundColor: const Color(0xFFF7F9FC),
              surfaceTintColor: const Color(0xFFF7F9FC),
              elevation: 0,
              automaticallyImplyLeading: false,
              title: Text(
                'TripwiseGO',
                style: GoogleFonts.instrumentSans(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
            ),
      body: SafeArea(
        child: IndexedStack(
          index: _currentIndex,
          children: [
            _chatPage, // Index 0: Wanderly Chat (previously Home)
            _buildPlanPage(), // Index 1: Plan/Itinerary (previously Chat)
            _matchPage, // Index 2: Match (unchanged)
            _buildCollaborativePage(), // Index 3: Collaborative Itinerary (standalone)
            _buildProfilePage(), // Index 4: Profile (unchanged)
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      margin: const EdgeInsets.all(16), // Add margins from edges
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(100), // Add rounded corners
        boxShadow: [
          // Simplified single shadow for better performance
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12, // Reduced from 20
            offset: const Offset(0, 3), // Reduced from 4
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(0, Icons.chat_bubble_outline, Icons.chat_bubble,
                  AppLocalizations.of(context).chat),
              _buildNavItem(1, Icons.list_alt_outlined, Icons.list_alt,
                  AppLocalizations.of(context).plan),
              _buildCenterNavItem(2, Icons.thumbs_up_down_outlined,
                  Icons.thumbs_up_down, AppLocalizations.of(context).match),
              _buildNavItem(3, Icons.group_outlined, Icons.group,
                  AppLocalizations.of(context).collaborativeItinerary),
              _buildNavItem(4, Icons.person_outline, Icons.person,
                  AppLocalizations.of(context).profile),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
      int index, IconData outlineIcon, IconData filledIcon, String label) {
    final isSelected = _currentIndex == index;

    return GestureDetector(
      onTap: () => _onNavItemTapped(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150), // Reduced from 200ms
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected ? filledIcon : outlineIcon,
              color: isSelected
                  ? const Color(0xFF0D76FF)
                  : const Color(0xFF718096),
              size: 24,
            ),
            const SizedBox(height: 4),
            AnimatedContainer(
              duration: const Duration(milliseconds: 150), // Reduced from 200ms
              width: isSelected ? 6 : 0,
              height: isSelected ? 6 : 0,
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCenterNavItem(
      int index, IconData outlineIcon, IconData filledIcon, String label) {
    final isSelected = _currentIndex == index;

    return GestureDetector(
      onTap: () => _onNavItemTapped(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150), // Reduced from 200ms
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF0D76FF) : const Color(0xFF0D76FF),
          borderRadius: BorderRadius.circular(28),
          boxShadow: [
            // Simplified single shadow for better performance
            BoxShadow(
              color:
                  const Color(0xFF0D76FF).withOpacity(0.25), // Reduced opacity
              blurRadius: 8, // Reduced from 12
              offset: const Offset(0, 2), // Reduced from 4
            ),
          ],
        ),
        child: Icon(
          isSelected ? filledIcon : outlineIcon,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  // Collaborative itinerary methods
  void _showJoinItineraryDialog() {
    final TextEditingController codeController = TextEditingController();
    bool isLoading = false;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: const Color(0xFFF7F9FC),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Text(
                'Join Itinerary',
                style: GoogleFonts.instrumentSans(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2D3748),
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Enter the 6-digit collaboration code to join a shared itinerary',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      color: const Color(0xFF718096),
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    controller: codeController,
                    textCapitalization: TextCapitalization.characters,
                    maxLength: 6,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 2,
                    ),
                    decoration: InputDecoration(
                      hintText: 'ABC123',
                      hintStyle: GoogleFonts.instrumentSans(
                        color: const Color(0xFF718096),
                        letterSpacing: 2,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                            color: Color(0xFF0D76FF), width: 2),
                      ),
                      counterText: '',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed:
                      isLoading ? null : () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF718096),
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                          final code = codeController.text.trim().toUpperCase();
                          if (code.length != 6) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Please enter a valid 6-digit code',
                                  style: GoogleFonts.instrumentSans(
                                      color: Colors.white),
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }

                          setState(() {
                            isLoading = true;
                          });

                          try {
                            final itinerary =
                                await CollaborativeItineraryService
                                    .joinItinerary(code);
                            if (mounted) {
                              Navigator.of(context).pop();
                              if (itinerary != null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Successfully joined "${itinerary.title}"',
                                      style: GoogleFonts.instrumentSans(
                                          color: Colors.white),
                                    ),
                                    backgroundColor: const Color(0xFF0D76FF),
                                  ),
                                );
                                _loadCollaborativeItineraries();
                                _navigateToCollaborativeItineraryDetail(
                                    itinerary);
                              }
                            }
                          } catch (e) {
                            if (mounted) {
                              setState(() {
                                isLoading = false;
                              });
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    e.toString().replaceAll('Exception: ', ''),
                                    style: GoogleFonts.instrumentSans(
                                        color: Colors.white),
                                  ),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0D76FF),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Join',
                          style: GoogleFonts.instrumentSans(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildCollaborativeItineraryCard(CollaborativeItinerary itinerary) {
    return GestureDetector(
      onTap: () => _navigateToCollaborativeItineraryDetail(itinerary),
      child: Container(
        height: 180,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Background Image
              Positioned.fill(
                child: _buildCollaborativeItineraryImage(itinerary),
              ),

              // Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                ),
              ),

              // Collaboration indicator
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0D76FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.people,
                        size: 12,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'SHARED',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Content
              Positioned(
                left: 20,
                bottom: 20,
                right: 20,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      itinerary.title,
                      style: GoogleFonts.instrumentSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: Colors.white.withOpacity(0.8),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          itinerary.dateRange,
                          style: GoogleFonts.instrumentSans(
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Code: ${itinerary.collaborationCode}',
                              style: GoogleFonts.instrumentSans(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        // Small collaboration icon
                        Container(
                          width: 60,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.group,
                            color: Colors.white.withOpacity(0.8),
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCollaborativeItineraryImage(CollaborativeItinerary itinerary) {
    // Use the same logic as regular itineraries but for collaborative ones
    if (itinerary.hasPhoto && itinerary.imagePath != null) {
      final imageFile = File(itinerary.imagePath!);
      if (imageFile.existsSync()) {
        return Image.file(
          imageFile,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildDefaultDestinationImage(
                itinerary.destinations.isNotEmpty
                    ? itinerary.destinations.first
                    : 'travel');
          },
        );
      }
    }
    return _buildDefaultDestinationImage(itinerary.destinations.isNotEmpty
        ? itinerary.destinations.first
        : 'travel');
  }

  void _navigateToCollaborativeItineraryDetail(
      CollaborativeItinerary itinerary) async {
    final result = await Navigator.of(context).push(
      NavigationHelper.createSlideRoute(
        CollaborativeItineraryDetailScreen(itinerary: itinerary),
      ),
    );

    // Refresh collaborative itineraries if any changes were made
    if (result == true) {
      _loadCollaborativeItineraries();
    }
  }

  Future<void> _loadCollaborativeItineraries() async {
    setState(() {
      _isLoadingCollaborativeItineraries = true;
    });

    try {
      final itineraries =
          await CollaborativeItineraryService.getUserCollaborativeItineraries();
      setState(() {
        _collaborativeItineraries = itineraries;
        _isLoadingCollaborativeItineraries = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingCollaborativeItineraries = false;
      });
      if (kDebugMode) {
        print('Error loading collaborative itineraries: $e');
      }
    }
  }
}
