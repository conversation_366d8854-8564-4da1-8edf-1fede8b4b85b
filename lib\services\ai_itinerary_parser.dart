import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/itinerary.dart';

/// Represents different types of structured content that can be parsed from AI responses
enum ContentType {
  itinerary,
  table,
  list,
  mixed,
  plain,
}

/// Represents a parsed table from AI response
class ParsedTable {
  final List<String> headers;
  final List<List<String>> rows;
  final String title;
  final String rawContent;

  ParsedTable({
    required this.headers,
    required this.rows,
    required this.title,
    required this.rawContent,
  });
}

/// Represents structured content parsed from AI response
class ParsedContent {
  final ContentType type;
  final String title;
  final String rawContent;
  final ParsedItinerary? itinerary;
  final List<ParsedTable>? tables;
  final List<String>? lists;
  final Map<String, dynamic>? metadata;

  ParsedContent({
    required this.type,
    required this.title,
    required this.rawContent,
    this.itinerary,
    this.tables,
    this.lists,
    this.metadata,
  });
}

/// Service to parse AI chat responses and extract structured content including itineraries, tables, and lists
class AIItineraryParser {
  /// Parse AI response and detect the type of structured content
  static ParsedContent parseStructuredContent(String aiResponse) {
    try {
      // Check for itinerary content first
      if (containsItinerary(aiResponse)) {
        final parsedItinerary = parseItinerary(aiResponse);
        if (parsedItinerary != null) {
          return ParsedContent(
            type: ContentType.itinerary,
            title: parsedItinerary.title ?? 'Travel Itinerary',
            rawContent: aiResponse,
            itinerary: parsedItinerary,
          );
        }
      }

      // Check for table content
      final tables = _parseTables(aiResponse);
      if (tables.isNotEmpty) {
        return ParsedContent(
          type: ContentType.table,
          title: tables.first.title,
          rawContent: aiResponse,
          tables: tables,
        );
      }

      // Check for list content
      final lists = _parseLists(aiResponse);
      if (lists.isNotEmpty) {
        return ParsedContent(
          type: ContentType.list,
          title: _extractContentTitle(aiResponse) ?? 'Travel Information',
          rawContent: aiResponse,
          lists: lists,
        );
      }

      // Check for mixed content (tables + itinerary + lists)
      if (tables.isNotEmpty || lists.isNotEmpty) {
        return ParsedContent(
          type: ContentType.mixed,
          title: _extractContentTitle(aiResponse) ?? 'Travel Guide',
          rawContent: aiResponse,
          tables: tables.isNotEmpty ? tables : null,
          lists: lists.isNotEmpty ? lists : null,
        );
      }

      // Default to plain content
      return ParsedContent(
        type: ContentType.plain,
        title: _extractContentTitle(aiResponse) ?? 'Travel Information',
        rawContent: aiResponse,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing structured content: $e');
      }
      return ParsedContent(
        type: ContentType.plain,
        title: 'Travel Information',
        rawContent: aiResponse,
      );
    }
  }

  /// Parse tables from AI response
  static List<ParsedTable> _parseTables(String aiResponse) {
    final tables = <ParsedTable>[];
    final lines = aiResponse.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // Look for table indicators
      if (_isTableLine(line)) {
        final table = _extractTable(lines, i);
        if (table != null) {
          tables.add(table);
        }
      }
    }

    return tables;
  }

  /// Check if a line indicates the start of a table
  static bool _isTableLine(String line) {
    // Look for markdown table syntax or pipe-separated values
    if (line.contains('|') && line.split('|').length >= 3) {
      return true;
    }

    // Look for table headers with common travel terms
    final tableIndicators = [
      'table',
      'comparison',
      'overview',
      'summary',
      'breakdown',
      'schedule',
      'timeline',
    ];

    final lowerLine = line.toLowerCase();
    return tableIndicators.any((indicator) => lowerLine.contains(indicator));
  }

  /// Extract a table starting from the given line index
  static ParsedTable? _extractTable(List<String> lines, int startIndex) {
    try {
      final headers = <String>[];
      final rows = <List<String>>[];
      String title = 'Table';

      // Look for title in previous lines
      for (int i = startIndex - 1; i >= 0 && i >= startIndex - 3; i--) {
        final prevLine = lines[i].trim();
        if (prevLine.isNotEmpty && !_isTableLine(prevLine)) {
          title = _cleanTableTitle(prevLine);
          break;
        }
      }

      // Parse table content
      bool foundHeaders = false;
      for (int i = startIndex; i < lines.length; i++) {
        final line = lines[i].trim();

        if (line.isEmpty) {
          if (foundHeaders && rows.isNotEmpty) break;
          continue;
        }

        if (line.contains('|')) {
          final cells = line
              .split('|')
              .map((cell) => cell.trim())
              .where((cell) => cell.isNotEmpty)
              .toList();

          if (cells.isNotEmpty) {
            if (!foundHeaders) {
              headers.addAll(cells);
              foundHeaders = true;
            } else if (!_isTableSeparator(line)) {
              rows.add(cells);
            }
          }
        } else if (foundHeaders) {
          // End of table
          break;
        }
      }

      if (headers.isNotEmpty && rows.isNotEmpty) {
        return ParsedTable(
          headers: headers,
          rows: rows,
          title: title,
          rawContent: lines
              .sublist(startIndex, startIndex + rows.length + 2)
              .join('\n'),
        );
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting table: $e');
      }
      return null;
    }
  }

  /// Check if a line is a table separator (like |---|---|)
  static bool _isTableSeparator(String line) {
    return line.contains('-') &&
        line.contains('|') &&
        line.replaceAll(RegExp(r'[|\-\s]'), '').isEmpty;
  }

  /// Clean and format table title
  static String _cleanTableTitle(String title) {
    // Remove markdown formatting
    String cleaned = title.replaceAll(RegExp(r'[#*_`]'), '').trim();

    // Capitalize first letter
    if (cleaned.isNotEmpty) {
      cleaned = cleaned[0].toUpperCase() + cleaned.substring(1);
    }

    return cleaned.isEmpty ? 'Table' : cleaned;
  }

  /// Parse lists from AI response
  static List<String> _parseLists(String aiResponse) {
    final lists = <String>[];
    final lines = aiResponse.split('\n');

    String currentList = '';
    bool inList = false;

    for (final line in lines) {
      final trimmedLine = line.trim();

      // Check if this is a list item
      if (_isListItem(trimmedLine)) {
        if (!inList) {
          inList = true;
          currentList = '';
        }
        currentList += '$trimmedLine\n';
      } else if (inList && trimmedLine.isEmpty) {
        // End of current list
        if (currentList.isNotEmpty) {
          lists.add(currentList.trim());
          currentList = '';
        }
        inList = false;
      } else if (inList && !_isListItem(trimmedLine)) {
        // End of list due to non-list content
        if (currentList.isNotEmpty) {
          lists.add(currentList.trim());
          currentList = '';
        }
        inList = false;
      }
    }

    // Add final list if we ended while in a list
    if (inList && currentList.isNotEmpty) {
      lists.add(currentList.trim());
    }

    return lists;
  }

  /// Check if a line is a list item
  static bool _isListItem(String line) {
    if (line.isEmpty) return false;

    // Check for bullet points
    final bulletPatterns = [
      RegExp(r'^\s*[•·▪▫‣⁃]\s+'),
      RegExp(r'^\s*[-*+]\s+'),
      RegExp(r'^\s*\d+\.\s+'),
      RegExp(r'^\s*[a-zA-Z]\.\s+'),
      RegExp(r'^\s*[ivxlcdm]+\.\s+', caseSensitive: false),
    ];

    return bulletPatterns.any((pattern) => pattern.hasMatch(line));
  }

  /// Extract content title from AI response
  static String? _extractContentTitle(String aiResponse) {
    final lines = aiResponse.split('\n');

    for (final line in lines) {
      final trimmedLine = line.trim();

      // Look for markdown headers
      if (trimmedLine.startsWith('#')) {
        return trimmedLine.replaceAll(RegExp(r'^#+\s*'), '').trim();
      }

      // Look for lines that seem like titles (short, capitalized, no punctuation at end)
      if (trimmedLine.isNotEmpty &&
          trimmedLine.length < 100 &&
          trimmedLine[0].toUpperCase() == trimmedLine[0] &&
          !trimmedLine.endsWith('.') &&
          !trimmedLine.endsWith('?') &&
          !trimmedLine.endsWith('!')) {
        return trimmedLine;
      }
    }

    return null;
  }

  /// Analyze user message to determine if they're requesting an itinerary
  static bool userRequestedItinerary(String userMessage) {
    final message = userMessage.toLowerCase();

    // Direct itinerary request patterns
    final itineraryRequestPatterns = [
      RegExp(
          r'\b(create|make|plan|build|generate)\s+(an?\s+)?(itinerary|travel\s+plan|trip\s+plan)\b'),
      RegExp(r'\b(plan\s+my\s+trip|plan\s+my\s+travel)\b'),
      RegExp(r'\b\d+\s*days?\s+(in|to|at|for)\s+[a-z]+.*itinerary\b'),
      RegExp(r'\bitinerary\s+for\s+\d+\s*days?\b'),
      RegExp(r'\b(suggest|recommend)\s+an?\s+itinerary\b'),
      RegExp(r'\bwhat\s+should\s+i\s+do\s+for\s+\d+\s*days?\s+in\b'),
    ];

    // Planning request patterns
    final planningRequestPatterns = [
      RegExp(r'\bplan\s+a\s+\d+\s*day\s+trip\b'),
      RegExp(r'\b\d+\s*day\s+trip\s+to\b'),
      RegExp(r'\bhow\s+to\s+spend\s+\d+\s*days?\s+in\b'),
      RegExp(r'\bwhat\s+to\s+do\s+in\s+\d+\s*days?\b'),
      RegExp(r'\b\d+\s*days?\s+schedule\s+for\b'),
    ];

    // Check for direct itinerary requests
    for (final pattern in itineraryRequestPatterns) {
      if (pattern.hasMatch(message)) {
        return true;
      }
    }

    // Check for planning requests
    for (final pattern in planningRequestPatterns) {
      if (pattern.hasMatch(message)) {
        return true;
      }
    }

    return false;
  }

  /// Check if user message is asking general travel questions (not itinerary requests)
  static bool isGeneralTravelQuestion(String userMessage) {
    final message = userMessage.toLowerCase();

    // General question patterns that are NOT itinerary requests
    final generalQuestionPatterns = [
      RegExp(r'\bwhat.?s\s+the\s+weather\s+like\b'),
      RegExp(r'\btell\s+me\s+about\b'),
      RegExp(r'\bwhat\s+are\s+the\s+best\s+(restaurants|hotels|attractions)\b'),
      RegExp(r'\bhow\s+much\s+does\s+it\s+cost\b'),
      RegExp(r'\bwhen\s+is\s+the\s+best\s+time\s+to\s+visit\b'),
      RegExp(r'\bwhat\s+language\s+do\s+they\s+speak\b'),
      RegExp(r'\bwhat\s+currency\s+do\s+they\s+use\b'),
      RegExp(r'\bhow\s+to\s+get\s+from\s+.+\s+to\s+.+\b'),
      RegExp(r'\bis\s+it\s+safe\s+to\s+travel\b'),
      RegExp(r'\bwhat\s+should\s+i\s+pack\b'),
    ];

    for (final pattern in generalQuestionPatterns) {
      if (pattern.hasMatch(message)) {
        return true;
      }
    }

    return false;
  }

  /// Check if the AI response contains a complete structured itinerary
  static bool containsItinerary(String aiResponse) {
    final response = aiResponse.toLowerCase();

    // First, check if this looks like a structured itinerary response
    if (!_hasItineraryStructure(response)) {
      return false;
    }

    // Then verify it has sufficient itinerary content
    return _hasItineraryContent(response);
  }

  /// Check if the response has the structural elements of an itinerary
  static bool _hasItineraryStructure(String response) {
    // Strong indicators of structured itinerary format
    final structuralPatterns = [
      RegExp(r'day \d+[:\-\s]'), // "Day 1:", "Day 2 -", etc.
      RegExp(r'\bday \d+\b.*\n.*\bday \d+\b'), // Multiple days mentioned
      RegExp(r'(morning|afternoon|evening):\s*[a-z]'), // Time-based structure
      RegExp(r'\d+\s*days?\s*(in|to|at)\s*[a-z]+'), // "5 days in Tokyo"
    ];

    // Count how many structural patterns match
    int structuralMatches = 0;
    for (final pattern in structuralPatterns) {
      if (pattern.hasMatch(response)) {
        structuralMatches++;
      }
    }

    // Need at least 1 strong structural indicator
    if (structuralMatches == 0) {
      return false;
    }

    // Check for day progression (Day 1, Day 2, etc.)
    final dayMatches = RegExp(r'day (\d+)').allMatches(response);
    final dayNumbers = dayMatches.map((m) => int.parse(m.group(1)!)).toSet();

    // Must have at least 2 consecutive days for a proper itinerary
    if (dayNumbers.length >= 2) {
      final sortedDays = dayNumbers.toList()..sort();
      for (int i = 0; i < sortedDays.length - 1; i++) {
        if (sortedDays[i + 1] == sortedDays[i] + 1) {
          return true; // Found consecutive days
        }
      }
    }

    // Alternative: check for comprehensive single-day detailed structure
    if (dayNumbers.length == 1) {
      // Must have detailed time-based structure for single day
      final timePatterns = [
        RegExp(r'morning[:\-\s]'),
        RegExp(r'afternoon[:\-\s]'),
        RegExp(r'evening[:\-\s]'),
        RegExp(r'\d{1,2}:\d{2}'),
      ];

      int timeMatches = 0;
      for (final pattern in timePatterns) {
        if (pattern.hasMatch(response)) {
          timeMatches++;
        }
      }

      return timeMatches >= 2; // Need at least 2 time indicators
    }

    return false;
  }

  /// Check if the response contains sufficient itinerary content
  static bool _hasItineraryContent(String response) {
    // Essential itinerary keywords (more specific than before)
    final essentialKeywords = [
      'itinerary',
      'travel plan',
      'trip plan',
      'schedule',
    ];

    // Activity indicators (must be in context of structured content)
    final activityIndicators = [
      'visit',
      'explore',
      'tour',
      'museum',
      'restaurant',
      'attraction',
      'temple',
      'park',
      'market',
      'shopping',
    ];

    // Check for essential keywords
    bool hasEssentialKeyword = false;
    for (final keyword in essentialKeywords) {
      if (response.contains(keyword)) {
        hasEssentialKeyword = true;
        break;
      }
    }

    // Count activity indicators
    int activityCount = 0;
    for (final activity in activityIndicators) {
      if (response.contains(activity)) {
        activityCount++;
      }
    }

    // Must have either essential keyword OR multiple activities (3+)
    return hasEssentialKeyword || activityCount >= 3;
  }

  /// Parse AI response and extract itinerary data
  static ParsedItinerary? parseItinerary(String aiResponse) {
    try {
      if (!containsItinerary(aiResponse)) {
        return null;
      }

      final lines = aiResponse.split('\n');
      final destinations = <String>[];
      final activities = <String, List<String>>{};
      final daySpecificActivities = <int, Map<String, List<String>>>{};
      String? title;
      String? startDate;
      String? endDate;
      int currentDay = 1;
      String? currentDestination;

      for (int i = 0; i < lines.length; i++) {
        final line = lines[i].trim();
        if (line.isEmpty) continue;

        // Extract title (usually the first meaningful line)
        if (title == null && _isLikelyTitle(line)) {
          title = _cleanTitle(line);
          continue;
        }

        // Extract day information
        final dayMatch =
            RegExp(r'day (\d+)', caseSensitive: false).firstMatch(line);
        if (dayMatch != null) {
          currentDay = int.parse(dayMatch.group(1)!);
          continue;
        }

        // Extract destinations
        final destination = _extractDestination(line);
        if (destination != null && !destinations.contains(destination)) {
          destinations.add(destination);
          currentDestination = destination;
          activities[destination] = [];
          continue;
        }

        // Extract activities
        final activity = _extractActivity(line);
        if (activity != null && currentDestination != null) {
          // Add to legacy format
          if (!activities.containsKey(currentDestination)) {
            activities[currentDestination] = [];
          }
          activities[currentDestination]!.add(activity);

          // Add to day-specific format
          if (!daySpecificActivities.containsKey(currentDay)) {
            daySpecificActivities[currentDay] = {};
          }
          if (!daySpecificActivities[currentDay]!
              .containsKey(currentDestination)) {
            daySpecificActivities[currentDay]![currentDestination] = [];
          }
          daySpecificActivities[currentDay]![currentDestination]!.add(activity);
        }

        // Extract dates
        if (startDate == null) {
          startDate = _extractDate(line);
        }
      }

      // Generate end date if not found
      if (endDate == null && startDate != null) {
        endDate = _generateEndDate(startDate, daySpecificActivities.length);
      }

      // Generate title if not found
      if (title == null) {
        title = _generateTitle(destinations);
      }

      // Validate that this is not a placeholder or generic response
      if (title != null && _isPlaceholderTitle(title)) {
        if (kDebugMode) {
          print('AI Itinerary Parser: Rejecting placeholder title: $title');
        }
        return null;
      }

      // Generate start date if not found
      if (startDate == null) {
        startDate = _generateDefaultStartDate();
      }

      if (destinations.isEmpty) {
        return null;
      }

      return ParsedItinerary(
        title: title,
        destinations: destinations,
        activities: activities,
        daySpecificActivities: daySpecificActivities,
        startDate: startDate,
        endDate: endDate ?? startDate,
        rawResponse: aiResponse,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing itinerary: $e');
      }
      return null;
    }
  }

  /// Check if a line is likely to be a title
  static bool _isLikelyTitle(String line) {
    final cleanLine = line.toLowerCase().trim();

    // Skip common non-title patterns
    if (cleanLine.startsWith('day ') ||
        cleanLine.startsWith('morning') ||
        cleanLine.startsWith('afternoon') ||
        cleanLine.startsWith('evening') ||
        cleanLine.contains(':') ||
        cleanLine.length < 5 ||
        cleanLine.length > 100) {
      return false;
    }

    // Look for title indicators
    final titleKeywords = [
      'trip',
      'itinerary',
      'adventure',
      'journey',
      'vacation',
      'travel',
      'tour',
      'visit',
      'explore',
    ];

    for (final keyword in titleKeywords) {
      if (cleanLine.contains(keyword)) {
        return true;
      }
    }

    // Check if it's a destination-based title
    final destinations = [
      'paris',
      'london',
      'tokyo',
      'rome',
      'new york',
      'bali'
    ];
    for (final dest in destinations) {
      if (cleanLine.contains(dest)) {
        return true;
      }
    }

    return false;
  }

  /// Clean and format title
  static String _cleanTitle(String title) {
    return title
        .replaceAll(RegExp(r'^#+\s*'), '') // Remove markdown headers
        .replaceAll(RegExp(r'\*+'), '') // Remove markdown bold/italic
        .trim();
  }

  /// Extract destination from line
  static String? _extractDestination(String line) {
    final cleanLine = line.toLowerCase().trim();

    // Skip obvious non-destinations
    if (cleanLine.startsWith('day ') ||
        cleanLine.contains(':') ||
        cleanLine.length < 3) {
      return null;
    }

    // Look for destination patterns
    final destinationPatterns = [
      RegExp(r'visit\s+([^,\n]+)', caseSensitive: false),
      RegExp(r'explore\s+([^,\n]+)', caseSensitive: false),
      RegExp(r'in\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)', caseSensitive: false),
      RegExp(r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)(?:\s*-|\s*:)',
          caseSensitive: false),
    ];

    for (final pattern in destinationPatterns) {
      final match = pattern.firstMatch(line);
      if (match != null) {
        return match.group(1)?.trim();
      }
    }

    return null;
  }

  /// Extract activity from line
  static String? _extractActivity(String line) {
    final cleanLine = line.trim();

    if (cleanLine.isEmpty || cleanLine.length < 5) {
      return null;
    }

    // Remove common prefixes
    String activity = cleanLine
        .replaceAll(RegExp(r'^[-•*]\s*'), '') // Remove bullet points
        .replaceAll(RegExp(r'^\d+\.\s*'), '') // Remove numbered lists
        .replaceAll(
            RegExp(r'^(morning|afternoon|evening):\s*', caseSensitive: false),
            '')
        .trim();

    if (activity.length < 5) {
      return null;
    }

    return activity;
  }

  /// Extract date from line
  static String? _extractDate(String line) {
    // Look for various date formats
    final datePatterns = [
      RegExp(r'\b(\d{1,2})/(\d{1,2})/(\d{4})\b'),
      RegExp(r'\b(\d{1,2})-(\d{1,2})-(\d{4})\b'),
      RegExp(
          r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2}),?\s+(\d{4})',
          caseSensitive: false),
      RegExp(
          r'\b(\d{1,2})\s+(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{4})',
          caseSensitive: false),
    ];

    for (final pattern in datePatterns) {
      final match = pattern.firstMatch(line);
      if (match != null) {
        return match.group(0);
      }
    }

    return null;
  }

  /// Generate end date based on start date and number of days
  static String _generateEndDate(String startDate, int numberOfDays) {
    try {
      // Simple date calculation - in a real app, use proper date parsing
      return startDate; // For now, return same date
    } catch (e) {
      return startDate;
    }
  }

  /// Generate title from destinations
  static String _generateTitle(List<String> destinations) {
    if (destinations.isEmpty) {
      return 'Travel Itinerary';
    }

    if (destinations.length == 1) {
      return '${destinations.first} Adventure';
    }

    if (destinations.length == 2) {
      return '${destinations.first} & ${destinations.last} Trip';
    }

    return '${destinations.first} & ${destinations.length - 1} More Destinations';
  }

  /// Generate default start date
  static String _generateDefaultStartDate() {
    final now = DateTime.now();
    final future = now.add(const Duration(days: 30));
    return '${future.month}/${future.day}/${future.year}';
  }

  /// Check if a title appears to be a placeholder or generic response
  static bool _isPlaceholderTitle(String title) {
    final lowerTitle = title.toLowerCase();
    return lowerTitle.contains('travel dreams') ||
        lowerTitle.contains('let\'s make') ||
        lowerTitle.contains('✨') ||
        lowerTitle.contains('placeholder') ||
        lowerTitle.contains('sample') ||
        lowerTitle.contains('demo') ||
        lowerTitle.contains('here\'s your') ||
        lowerTitle.contains('i\'d be happy') ||
        lowerTitle.contains('i can help') ||
        lowerTitle.contains('great question') ||
        lowerTitle.contains('perfect!') ||
        lowerTitle.contains('wonderful!') ||
        lowerTitle.length < 3 ||
        lowerTitle.length > 100;
  }
}

/// Parsed itinerary data structure
class ParsedItinerary {
  final String title;
  final List<String> destinations;
  final Map<String, List<String>> activities;
  final Map<int, Map<String, List<String>>> daySpecificActivities;
  final String startDate;
  final String endDate;
  final String rawResponse;

  ParsedItinerary({
    required this.title,
    required this.destinations,
    required this.activities,
    required this.daySpecificActivities,
    required this.startDate,
    required this.endDate,
    required this.rawResponse,
  });

  /// Convert to Itinerary model
  Itinerary toItinerary() {
    return Itinerary(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      startDate: startDate,
      endDate: endDate,
      destinations: destinations,
      hasPhoto: false,
      imagePath: null,
      dailyActivities: activities,
      daySpecificActivities:
          daySpecificActivities.isNotEmpty ? daySpecificActivities : null,
      accommodation: '',
      additionalNotes: 'Generated from AI chat response',
      createdAt: DateTime.now(),
    );
  }
}
