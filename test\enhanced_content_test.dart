import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/services/ai_itinerary_parser.dart';

void main() {
  group('Enhanced Content Parsing Tests', () {
    test('should parse table content correctly', () {
      const tableResponse = '''
Here's a quick comparison table for your trip:

| Destination | Best Time | Budget | Duration |
|-------------|-----------|--------|----------|
| Paris | Spring | \$2000 | 5 days |
| Tokyo | Fall | \$3000 | 7 days |
| Bali | Summer | \$1500 | 6 days |

This table shows the key details for each destination.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(tableResponse);

      // The content might be detected as table, mixed, or itinerary depending on parsing logic
      expect(parsedContent.type,
          isIn([ContentType.table, ContentType.mixed, ContentType.itinerary]));
      expect(parsedContent.rawContent, contains('Destination'));
      expect(parsedContent.rawContent, contains('Paris'));
      expect(parsedContent.rawContent, contains('Tokyo'));
      expect(parsedContent.rawContent, contains('Bali'));
    });

    test('should parse itinerary content correctly', () {
      const itineraryResponse = '''
# 5-Day Paris Itinerary

**Day 1: Arrival and City Center**
- Check into hotel
- Visit Notre-Dame Cathedral
- Walk along the Seine River
- Dinner at a local bistro

**Day 2: Museums and Culture**
- Morning at the Louvre Museum
- Afternoon at Musée d'Orsay
- Evening stroll through Montmartre

**Day 3: Iconic Landmarks**
- Visit the Eiffel Tower
- Explore Champs-Élysées
- Arc de Triomphe
- Shopping at Galeries Lafayette
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(itineraryResponse);

      expect(parsedContent.type, ContentType.itinerary);
      expect(parsedContent.itinerary, isNotNull);

      final itinerary = parsedContent.itinerary!;
      expect(itinerary.title, contains('Paris'));
      expect(itinerary.daySpecificActivities, isNotNull);
      expect(itinerary.daySpecificActivities!.keys.length, greaterThan(0));
    });

    test('should parse list content correctly', () {
      const listResponse = '''
Here are the top travel tips for your trip:

• Pack light and bring versatile clothing
• Always carry a portable charger
• Download offline maps before traveling
• Keep copies of important documents
• Learn basic phrases in the local language

Additional recommendations:
- Book accommodations in advance
- Research local customs and etiquette
- Consider travel insurance
- Exchange currency before departure
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(listResponse);

      expect(parsedContent.type, ContentType.list);
      expect(parsedContent.lists, isNotNull);
      expect(parsedContent.lists!.length, greaterThan(0));
    });

    test('should handle mixed content correctly', () {
      const mixedResponse = '''
# Travel Planning Guide

Here's a comparison of destinations:

| City | Cost | Rating |
|------|------|--------|
| Paris | High | 5/5 |
| Rome | Medium | 4/5 |

**Packing Checklist:**
• Passport and visa
• Travel insurance documents
• Comfortable walking shoes
• Weather-appropriate clothing

**Day 1 Itinerary:**
- Morning: Arrival and hotel check-in
- Afternoon: City walking tour
- Evening: Welcome dinner
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(mixedResponse);

      // Should detect as mixed content or the most prominent type
      expect(parsedContent.type,
          isIn([ContentType.mixed, ContentType.table, ContentType.itinerary]));
      expect(parsedContent.title, isNotEmpty);
    });

    test('should handle plain content correctly', () {
      const plainResponse = '''
Paris is a beautiful city with rich history and culture. 
The best time to visit is during spring when the weather is mild.
You should definitely try the local cuisine and visit the major landmarks.
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(plainResponse);

      expect(parsedContent.type, ContentType.plain);
      expect(parsedContent.title, isNotEmpty);
      expect(parsedContent.rawContent, equals(plainResponse));
    });
  });

  group('Integration Tests', () {
    test('should handle empty content', () {
      const emptyResponse = '';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(emptyResponse);

      expect(parsedContent.type, ContentType.plain);
      expect(parsedContent.rawContent, equals(emptyResponse));
    });

    test('should handle content with special characters', () {
      const specialResponse = '''
Travel costs in €:
• Paris: €100/day
• Rome: €80/day
• Barcelona: €70/day
''';

      final parsedContent =
          AIItineraryParser.parseStructuredContent(specialResponse);

      expect(parsedContent.type, isIn([ContentType.list, ContentType.plain]));
      expect(parsedContent.rawContent, contains('€'));
    });
  });
}
