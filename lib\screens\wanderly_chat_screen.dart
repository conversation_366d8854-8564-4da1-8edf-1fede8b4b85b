import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/gemini_service.dart';
import '../services/suggestion_generator_service.dart';
import '../services/chat_history_service.dart';
import '../services/google_search_service.dart';
import '../models/chat_history_models.dart';
import '../widgets/travel_enhanced_markdown.dart';
import '../widgets/chat_history_sidebar.dart';
import '../widgets/web_search_results.dart';
import '../widgets/model_selector.dart';
import '../services/feedback_service.dart';
import '../models/feedback_models.dart';
import '../widgets/feedback_dialog.dart';
import '../services/ai_learning_service.dart';
import '../services/learning_configuration_service.dart';
import '../services/plan_navigation_service.dart';
import '../services/enhanced_content_service.dart';
import '../services/ai_itinerary_parser.dart';
import '../widgets/enhanced_content_display.dart';
import '../widgets/interactive_itinerary_widget.dart';
import '../generated/l10n/app_localizations.dart';

// Enums and classes for flexible chat layout
enum MessageSize { small, medium, large }

class MessageRowData {
  final Widget widget;
  final int nextIndex;

  MessageRowData({
    required this.widget,
    required this.nextIndex,
  });
}

// Enums for response length categorization
enum ResponseLength { short, long }

class QuestionAnalysis {
  final ResponseLength responseLength;
  final String category;
  final String reasoning;

  QuestionAnalysis({
    required this.responseLength,
    required this.category,
    required this.reasoning,
  });
}

class WanderlyChatScreen extends StatefulWidget {
  final Function(int)? onNavigateToTab;

  const WanderlyChatScreen({
    super.key,
    this.onNavigateToTab,
  });

  @override
  State<WanderlyChatScreen> createState() => _WanderlyChatScreenState();
}

class _WanderlyChatScreenState extends State<WanderlyChatScreen>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();

  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;
  late AnimationController _typingAnimationController;
  late Animation<double> _typingAnimation;
  late AnimationController _stopButtonAnimationController;
  late Animation<double> _stopButtonFadeAnimation;
  late Animation<Offset> _stopButtonSlideAnimation;
  late AnimationController _textAnimationController;
  late Animation<double> _textScaleAnimation;
  late Animation<double> _textFadeAnimation;
  late AnimationController _bubbleAnimationController;
  late Animation<double> _bubbleScaleAnimation;
  late Animation<double> _bubbleFadeAnimation;

  // TabController for the AppBar tabs
  late TabController _tabController;

  // Enhanced content for Plan tab
  List<EnhancedContentItem> _enhancedContent = [];
  bool _isLoadingEnhancedContent = false;

  List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isInitializing = false;
  bool _isGenerating = false;
  bool _showTabBar = false; // Controls AppBar transition
  int _selectedTabIndex = 0; // 0 for Chat, 1 for Plan

  // Chat history state
  bool _isSidebarVisible = false;
  ChatSession? _currentChatSession;
  bool _isHistoryInitialized = false;

  // For stop generation functionality
  Completer<void>? _currentGenerationCompleter;
  bool _shouldStopGeneration = false;

  // Image picker
  final ImagePicker _imagePicker = ImagePicker();

  // Speech to text
  final SpeechToText _speechToText = SpeechToText();
  bool _speechEnabled = false;
  bool _isListening = false;
  String _lastWords = '';

  // Web search
  bool _isSearching = false;
  bool _isWebSearchMode = false;

  // Voice recording animation controllers
  late AnimationController _voiceButtonAnimationController;
  late Animation<double> _voiceButtonScaleAnimation;
  late Animation<double> _voiceButtonPulseAnimation;
  late AnimationController _voiceRecordingAnimationController;
  late Animation<double> _voiceRecordingAnimation;

  // Dynamic suggestions
  List<String> _suggestions = [];
  List<String> _bottomSuggestions = [];

  // AutomaticKeepAliveClientMixin implementation
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    if (kDebugMode) {
      print('Chat Persistence: WanderlyChatScreen initState called');
    }

    // Main fade animation
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    // Typing animation
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _typingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingAnimationController,
      curve: Curves.easeInOut,
    ));

    // Stop button animation
    _stopButtonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _stopButtonFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stopButtonAnimationController,
      curve: Curves.easeInOut,
    ));

    _stopButtonSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _stopButtonAnimationController,
      curve: Curves.easeInOut,
    ));

    // Text animation
    _textAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _textScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textAnimationController,
      curve: Curves.elasticOut,
    ));

    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textAnimationController,
      curve: Curves.easeInOut,
    ));

    // Bubble animation
    _bubbleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _bubbleScaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bubbleAnimationController,
      curve: Curves.easeInOut,
    ));

    _bubbleFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bubbleAnimationController,
      curve: Curves.easeInOut,
    ));

    // Voice button animation
    _voiceButtonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _voiceButtonScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.15,
    ).animate(CurvedAnimation(
      parent: _voiceButtonAnimationController,
      curve: Curves.easeInOut,
    ));

    _voiceButtonPulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _voiceButtonAnimationController,
      curve: Curves.easeInOut,
    ));

    // Voice recording animation
    _voiceRecordingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _voiceRecordingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _voiceRecordingAnimationController,
      curve: Curves.easeInOut,
    ));

    // TabController for AppBar tabs
    _tabController = TabController(length: 2, vsync: this);

    _fadeAnimationController.forward();
    _textAnimationController.forward();

    // Initialize suggestions immediately (synchronous)
    _initializeSuggestions();

    // Initialize other services asynchronously to prevent blocking UI
    _initializeServicesAsync();
  }

  /// Initialize all services asynchronously to prevent UI blocking
  Future<void> _initializeServicesAsync() async {
    try {
      // Run all initializations concurrently but don't await them in initState
      // This prevents blocking the UI thread

      // Initialize speech to text (non-blocking)
      _initializeSpeech().catchError((error) {
        if (kDebugMode) {
          print('Speech initialization failed: $error');
        }
      });

      // Initialize Gemini service (non-blocking)
      _initializeService().catchError((error) {
        if (kDebugMode) {
          print('Gemini service initialization failed: $error');
        }
      });

      // Initialize AI learning system (non-blocking)
      _initializeLearningSystem().catchError((error) {
        if (kDebugMode) {
          print('Learning system initialization failed: $error');
        }
      });

      // Initialize chat history and enhanced content (non-blocking)
      _initializeChatHistory().catchError((error) {
        if (kDebugMode) {
          print('Chat history initialization failed: $error');
        }
      });

      _loadEnhancedContent().catchError((error) {
        if (kDebugMode) {
          print('Enhanced content loading failed: $error');
        }
      });
    } catch (error) {
      if (kDebugMode) {
        print('Service initialization error: $error');
      }
      // Don't rethrow - we want the UI to continue working even if services fail
    }
  }

  @override
  void dispose() {
    if (kDebugMode) {
      print('Chat Persistence: WanderlyChatScreen dispose called');
    }

    _fadeAnimationController.dispose();
    _typingAnimationController.dispose();
    _stopButtonAnimationController.dispose();
    _textAnimationController.dispose();
    _bubbleAnimationController.dispose();
    _voiceButtonAnimationController.dispose();
    _voiceRecordingAnimationController.dispose();
    _tabController.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    _focusNode.dispose();
    _currentGenerationCompleter?.complete();
    super.dispose();
  }

  Future<void> _initializeSpeech() async {
    try {
      _speechEnabled = await _speechToText.initialize(
        onStatus: (status) {
          if (kDebugMode) {
            debugPrint('Speech status: $status');
          }
        },
        onError: (error) {
          if (kDebugMode) {
            debugPrint('Speech error: $error');
          }
          setState(() {
            _isListening = false;
          });
          _voiceButtonAnimationController.reverse();
          _voiceRecordingAnimationController.stop();

          // Clear the text field on error
          if (_messageController.text == 'Listening...') {
            _messageController.clear();
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to initialize speech: $e');
      }
      _speechEnabled = false;
    }
  }

  Future<void> _initializeService() async {
    setState(() {
      _isInitializing = true;
    });

    try {
      await GeminiService.initialize();
      // Don't add initial greeting to messages list - it will be shown in initial state UI
    } catch (error) {
      _addErrorMessage(
          'Failed to initialize AI service. Some features may be limited.');
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  /// Initialize AI learning system in background
  Future<void> _initializeLearningSystem() async {
    try {
      // Initialize learning configuration service
      await LearningConfigurationService.instance.initialize();

      // Initialize AI learning service if enabled
      if (LearningConfigurationService.instance.isLearningAllowed()) {
        await AILearningService.instance.initialize();

        if (kDebugMode) {
          print('Chat Screen: AI learning system initialized successfully');
        }
      } else {
        if (kDebugMode) {
          print('Chat Screen: AI learning disabled by user settings');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Screen: Failed to initialize learning system - $e');
      }
      // Don't show error to user as this is a background feature
    }
  }

  void _initializeSuggestions() {
    // Initialize with default suggestions for new conversations
    _suggestions =
        SuggestionGeneratorService.generateSuggestions('', isInitial: true);
    _bottomSuggestions = [
      'Generate Summary',
      'Create Itinerary',
      'What do I have to learn about'
    ];
  }

  void _updateSuggestionsAfterAIResponse(String aiResponse,
      {String? userMessage}) {
    setState(() {
      // Generate contextual suggestions based on AI response
      _suggestions = SuggestionGeneratorService.generateSuggestions(aiResponse);

      // Generate dynamic bottom suggestions
      final followUps =
          SuggestionGeneratorService.generateFollowUpQuestions(aiResponse);
      final contextual =
          SuggestionGeneratorService.generateSuggestions(aiResponse);

      // Combine follow-ups with contextual suggestions for bottom area
      _bottomSuggestions = [
        ...followUps,
        ...contextual.take(3 - followUps.length),
      ].take(3).toList();

      // Fallback to generic suggestions if we don't have enough
      if (_bottomSuggestions.length < 3) {
        _bottomSuggestions.addAll([
          'Tell me more details',
          'Alternative options',
          'Budget considerations'
        ].take(3 - _bottomSuggestions.length));
      }
    });

    // Check if AI response contains itinerary and handle navigation
    PlanNavigationService.handleAIResponse(
      aiResponse,
      context,
      _navigateToPlanTab,
      userMessage: userMessage,
    );

    // Process AI response for enhanced content storage
    EnhancedContentService.processAIMessage(
      aiResponse,
      chatSessionId: _currentChatSession?.id,
      messageId: DateTime.now().millisecondsSinceEpoch.toString(),
    ).then((_) {
      // Refresh enhanced content if we're on the plan tab or if content was added
      if (_selectedTabIndex == 1 || _enhancedContent.isNotEmpty) {
        _loadEnhancedContent();
      }
    });
  }

  void _addErrorMessage(String message) {
    setState(() {
      _messages.add(
        ChatMessage(
          text: message,
          isUser: false,
          timestamp: DateTime.now(),
        ),
      );
    });
  }

  // Chat History Methods
  Future<void> _initializeChatHistory() async {
    try {
      if (kDebugMode) {
        print('Chat Persistence: Initializing chat history...');
      }

      await ChatHistoryService.initialize();

      // Always start with a fresh greeting screen on app restart
      // Don't automatically load the active session - let users access it via history button
      if (kDebugMode) {
        print('Chat Persistence: Starting with fresh greeting screen');
      }

      // Clear any active session to ensure we start fresh
      await ChatHistoryService.clearActiveSession();

      setState(() {
        _isHistoryInitialized = true;
        _currentChatSession = null;
        _messages.clear();
        _showTabBar = false;
      });

      if (kDebugMode) {
        print('Chat Persistence: Initialization complete - starting fresh');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Persistence Error: Failed to initialize chat history - $e');
      }
      // Set initialized to true even on error to prevent blocking the UI
      setState(() {
        _isHistoryInitialized = true;
      });
    }
  }

  Future<void> _loadChatSession(ChatSession session) async {
    try {
      if (kDebugMode) {
        print(
            'Chat Persistence: Loading session ${session.id} with ${session.messages.length} messages');
      }

      // Get the latest version of the session from storage to ensure we have the most recent data
      final latestSession = await ChatHistoryService.getSession(session.id);
      final sessionToLoad = latestSession ?? session;

      setState(() {
        _currentChatSession = sessionToLoad;
        _messages = sessionToLoad.messages
            .map((msg) => ChatMessage(
                  text: msg.text,
                  isUser: msg.isUser,
                  timestamp: msg.timestamp,
                  imagePath: msg.imagePath,
                  isImageMessage: msg.isImageMessage,
                  isWebSearchMessage: msg.isWebSearchMessage,
                  searchQuery: msg.searchQuery,
                  searchResult: ChatMessageModel.deserializeSearchResult(
                      msg.searchResultData),
                ))
            .toList();
        _showTabBar = _messages.isNotEmpty;
      });

      // Set this session as active in the service
      await ChatHistoryService.setActiveSession(sessionToLoad.id);

      // Update suggestions based on the last AI message
      if (_messages.isNotEmpty) {
        final lastAIMessage = _messages.lastWhere(
          (msg) => !msg.isUser,
          orElse: () => _messages.last,
        );
        if (!lastAIMessage.isUser) {
          _updateSuggestionsAfterAIResponse(lastAIMessage.text);
        }
      }

      if (kDebugMode) {
        print(
            'Chat Persistence: Successfully loaded ${_messages.length} messages');
      }

      // Scroll to bottom after loading messages
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (e) {
      if (kDebugMode) {
        print('Chat Persistence Error: Failed to load chat session - $e');
      }
      // Fallback to the provided session data
      setState(() {
        _currentChatSession = session;
        _messages = session.messages
            .map((msg) => ChatMessage(
                  text: msg.text,
                  isUser: msg.isUser,
                  timestamp: msg.timestamp,
                  imagePath: msg.imagePath,
                  isImageMessage: msg.isImageMessage,
                  isWebSearchMessage: msg.isWebSearchMessage,
                  searchQuery: msg.searchQuery,
                  searchResult: ChatMessageModel.deserializeSearchResult(
                      msg.searchResultData),
                ))
            .toList();
        _showTabBar = _messages.isNotEmpty;
      });
    }
  }

  Future<void> _createNewChatSession() async {
    try {
      if (kDebugMode) {
        print('Chat Persistence: Creating new chat session...');
      }

      final newSession = await ChatHistoryService.createNewSession();

      setState(() {
        _currentChatSession = newSession;
        _messages.clear();
        _showTabBar = false;
      });

      if (kDebugMode) {
        print(
            'Chat Persistence: New session created with ID: ${newSession.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Chat Persistence Error: Failed to create new chat session - $e');
      }
      // Clear the current session state even on error
      setState(() {
        _currentChatSession = null;
        _messages.clear();
        _showTabBar = false;
      });
    }
  }

  Future<void> _saveMessageToHistory(ChatMessage message) async {
    if (_currentChatSession != null) {
      try {
        final messageMap = {
          'text': message.text,
          'isUser': message.isUser,
          'timestamp': message.timestamp,
          'imagePath': message.imagePath,
          'isImageMessage': message.isImageMessage,
          'isWebSearchMessage': message.isWebSearchMessage,
          'searchQuery': message.searchQuery,
          'searchResultData':
              ChatMessageModel.serializeSearchResult(message.searchResult),
        };

        await ChatHistoryService.addMessageToSession(
            _currentChatSession!.id, messageMap);

        // Update session title if this is the first user message
        if (message.isUser && _currentChatSession!.messages.length == 1) {
          await ChatHistoryService.updateSessionTitle(
              _currentChatSession!.id, message.text);
        }

        // Refresh the current session from storage to ensure consistency
        final updatedSession =
            await ChatHistoryService.getSession(_currentChatSession!.id);
        if (updatedSession != null) {
          _currentChatSession = updatedSession;
          if (kDebugMode) {
            print(
                'Chat Persistence: Session refreshed with ${updatedSession.messages.length} messages');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('Chat Persistence Error: Failed to save message - $e');
        }
        // Don't throw the error to avoid breaking the chat flow
      }
    } else {
      if (kDebugMode) {
        print(
            'Chat Persistence Warning: No current session to save message to');
      }
    }
  }

  void _toggleSidebar() {
    setState(() {
      _isSidebarVisible = !_isSidebarVisible;
    });
  }

  void _onChatSelected(ChatSession session) async {
    try {
      if (kDebugMode) {
        print('Chat Persistence: User selected chat session ${session.id}');
      }

      // Close sidebar first for better UX
      setState(() {
        _isSidebarVisible = false;
      });

      // Load the selected session
      await _loadChatSession(session);
    } catch (e) {
      if (kDebugMode) {
        print('Chat Persistence Error: Failed to select chat session - $e');
      }
      // Close sidebar even on error
      setState(() {
        _isSidebarVisible = false;
      });
    }
  }

  void _onNewChat() async {
    try {
      if (kDebugMode) {
        print('Chat Persistence: Creating new chat session');
      }
      await _createNewChatSession();
    } catch (e) {
      if (kDebugMode) {
        print('Chat Persistence Error: Failed to create new chat session - $e');
      }
    }
  }

  Future<void> _startListening() async {
    if (!_speechEnabled) {
      // Request microphone permission
      final status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.of(context).microphonePermissionRequired,
                style: GoogleFonts.instrumentSans(),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
        return;
      }

      // Try to initialize speech again
      await _initializeSpeech();
      if (!_speechEnabled) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.of(context).speechRecognitionNotAvailable,
                style: GoogleFonts.instrumentSans(),
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
        return;
      }
    }

    if (!_isListening) {
      setState(() {
        _isListening = true;
        _lastWords = '';
      });

      // Show "Listening..." in the text field
      if (mounted) {
        _messageController.text = AppLocalizations.of(context).listening;
      }

      // Start animations
      _voiceButtonAnimationController.forward();
      _voiceRecordingAnimationController.repeat();

      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _lastWords = result.recognizedWords;
            // Update text field with real-time transcription
            if (_lastWords.isNotEmpty) {
              _messageController.text = _lastWords;
            } else {
              _messageController.text = AppLocalizations.of(context).listening;
            }
          });
        },
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        localeId: 'en_US', // You can add Indonesian support with 'id_ID'
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: true,
          listenMode: ListenMode.confirmation,
        ),
      );
    }
  }

  Future<void> _stopListening() async {
    if (_isListening) {
      await _speechToText.stop();
      setState(() {
        _isListening = false;
      });

      // Stop animations
      _voiceButtonAnimationController.reverse();
      _voiceRecordingAnimationController.stop();

      // Keep the final transcribed text in the text field for editing
      if (_lastWords.isNotEmpty) {
        _messageController.text = _lastWords;
        // Position cursor at the end of the text
        _messageController.selection = TextSelection.fromPosition(
          TextPosition(offset: _lastWords.length),
        );
      } else {
        // Clear the text field if no speech was detected
        _messageController.clear();
      }
    }
  }

  bool _hasUserMessages() {
    return _messages.any((message) => message.isUser);
  }

  // Question analysis for contextual response length
  QuestionAnalysis _analyzeQuestion(String question) {
    final lowerQuestion = question.toLowerCase().trim();

    // Short response patterns
    final shortPatterns = [
      // Simple factual questions
      RegExp(
          r'^(what|how much|how many|when|where|which)\s+(is|are|does|do|did|will|would|can|could)\s+'),
      RegExp(r'^(is|are|does|do|did|will|would|can|could)\s+'),

      // Yes/no questions
      RegExp(r'^(is|are|does|do|did|will|would|can|could|should)\s+.*\?'),

      // Simple greetings
      RegExp(r'^(hi|hello|hey|good morning|good afternoon|good evening)'),

      // Quick facts
      RegExp(
          r"^(what's|whats)\s+(the|a)\s+(weather|temperature|time|cost|price|currency)"),
      RegExp(r'^(how much|how many)\s+(does|do|is|are)\s+'),

      // Simple definitions
      RegExp(r'^(what is|what are|define|meaning of)\s+'),

      // Quick confirmations
      RegExp(r'^(ok|okay|thanks|thank you|got it|understood|yes|no|sure)$'),

      // Simple visa/document questions
      RegExp(r'^(do i need|is.*required|what.*visa)'),
    ];

    // Long response patterns
    final longPatterns = [
      // Itinerary planning
      RegExp(
          r'(plan|planning|itinerary|schedule|trip|vacation|holiday|journey)'),
      RegExp(r'(day|days|week|weeks|month|months).*trip'),
      RegExp(r'(suggest|recommend|advice|help).*trip'),

      // Destination suggestions
      RegExp(
          r'(where should|where can|best places|top destinations|recommend.*place)'),
      RegExp(r'(places to (visit|go|see|stay|eat))'),

      // Travel tips and advice
      RegExp(
          r'(tips|advice|guide|how to|best way|what to (pack|bring|wear|expect))'),
      RegExp(r'(travel.*advice|travel.*tips|travel.*guide)'),

      // Complex planning
      RegExp(r'(budget|cost|expense|money).*trip'),
      RegExp(r'(first time|never been|new to)'),
      RegExp(r'(with (kids|children|family|friends|partner))'),

      // Multi-part questions
      RegExp(r'(and|also|plus|additionally|furthermore)'),
      RegExp(r'(step by step|detailed|comprehensive|complete)'),

      // Specific travel scenarios
      RegExp(r'(honeymoon|anniversary|birthday|celebration)'),
      RegExp(r'(solo travel|group travel|family trip)'),
      RegExp(r'(adventure|romantic|relaxing|cultural|historical)'),
    ];

    // Check for short patterns first
    for (final pattern in shortPatterns) {
      if (pattern.hasMatch(lowerQuestion)) {
        return QuestionAnalysis(
          responseLength: ResponseLength.short,
          category: 'Simple Query',
          reasoning: 'Matches short response pattern: ${pattern.pattern}',
        );
      }
    }

    // Check for long patterns
    for (final pattern in longPatterns) {
      if (pattern.hasMatch(lowerQuestion)) {
        return QuestionAnalysis(
          responseLength: ResponseLength.long,
          category: 'Complex Planning',
          reasoning: 'Matches long response pattern: ${pattern.pattern}',
        );
      }
    }

    // Default logic based on question characteristics
    final wordCount = lowerQuestion.split(' ').length;
    final hasQuestionMark = lowerQuestion.contains('?');
    final hasMultipleSentences = lowerQuestion.split('.').length > 1 ||
        lowerQuestion.split('?').length > 2;

    // Short for simple, direct questions
    if (wordCount <= 8 && hasQuestionMark && !hasMultipleSentences) {
      return QuestionAnalysis(
        responseLength: ResponseLength.short,
        category: 'Simple Question',
        reasoning: 'Short, direct question ($wordCount words)',
      );
    }

    // Long for complex or multi-part queries
    if (wordCount > 15 || hasMultipleSentences) {
      return QuestionAnalysis(
        responseLength: ResponseLength.long,
        category: 'Complex Query',
        reasoning: 'Long or multi-part question ($wordCount words)',
      );
    }

    // Default to short for ambiguous cases
    return QuestionAnalysis(
      responseLength: ResponseLength.short,
      category: 'General Query',
      reasoning: 'Default classification for ambiguous input',
    );
  }

  Future<void> _createNewChat() async {
    try {
      if (kDebugMode) {
        print('Chat Persistence: Creating new chat session from add button');
      }

      // Stop any ongoing generation
      if (_currentGenerationCompleter != null &&
          !_currentGenerationCompleter!.isCompleted) {
        _currentGenerationCompleter!.complete();
      }
      _currentGenerationCompleter = null;

      // Create new chat session immediately without dialog
      await _createNewChatSession();
    } catch (e) {
      if (kDebugMode) {
        print('Chat Persistence Error: Failed to create new chat session - $e');
      }
    }
  }

  Future<void> _deleteCurrentChat() async {
    if (_currentChatSession == null) return;

    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            AppLocalizations.of(context).deleteChat,
            style: GoogleFonts.instrumentSans(
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this chat? This action cannot be undone.',
            style: GoogleFonts.instrumentSans(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'Delete',
                style: GoogleFonts.instrumentSans(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (shouldDelete == true) {
      // Stop any ongoing generation
      if (_currentGenerationCompleter != null &&
          !_currentGenerationCompleter!.isCompleted) {
        _currentGenerationCompleter!.complete();
      }
      _currentGenerationCompleter = null;

      // Delete the current chat session
      await ChatHistoryService.deleteSession(_currentChatSession!.id);

      // Reset to initial state
      setState(() {
        _currentChatSession = null;
        _messages.clear();
        _showTabBar = false;
        _isTyping = false;
        _isGenerating = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Chat deleted successfully',
              style: GoogleFonts.instrumentSans(),
            ),
            backgroundColor: const Color(0xFF0D76FF),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  Widget _buildTopNavigation() {
    if (!_hasUserMessages()) {
      // Initial state - show AI Assistant text centered and history button on the right
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Stack(
          children: [
            // Centered AI Assistant text
            Center(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  AppLocalizations.of(context).aiAssistant,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            // History button positioned on the right
            Positioned(
              right: 0,
              child: IconButton(
                onPressed: _toggleSidebar,
                icon: const Icon(
                  Icons.history,
                  color: Color(0xFF718096),
                ),
                tooltip: AppLocalizations.of(context).chatHistory,
              ),
            ),
          ],
        ),
      );
    }

    // After first message - show add button, tabs, and history button
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Add button (for new chat)
          IconButton(
            onPressed: _createNewChat,
            icon: const Icon(
              Icons.add,
              color: Color(0xFF0D76FF),
              size: 24,
            ),
            tooltip: AppLocalizations.of(context).newChat,
          ),

          // TabBar in the center (copied from match_tab_screen.dart)
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(100),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                onTap: (index) {
                  setState(() {
                    _selectedTabIndex = index;
                  });

                  // Load enhanced content when switching to Plan tab
                  if (index == 1) {
                    _loadEnhancedContent();
                  }
                },
                physics: const NeverScrollableScrollPhysics(),
                indicator: BoxDecoration(
                  color: const Color(0xFF0D76FF),
                  borderRadius: BorderRadius.circular(100),
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                labelColor: Colors.white,
                unselectedLabelColor: const Color(0xFF718096),
                labelStyle: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                tabs: const [
                  Tab(
                    text: 'Chat',
                    height: 40,
                  ),
                  Tab(
                    text: 'Plan',
                    height: 40,
                  ),
                ],
              ),
            ),
          ),

          // History button
          IconButton(
            onPressed: _toggleSidebar,
            icon: const Icon(
              Icons.history,
              color: Color(0xFF718096),
            ),
            tooltip: AppLocalizations.of(context).chatHistory,
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                AppLocalizations.of(context).addImage,
                style: GoogleFonts.instrumentSans(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: _buildImagePickerOption(
                      icon: Icons.camera_alt,
                      label: AppLocalizations.of(context).camera,
                      onTap: () {
                        Navigator.pop(context);
                        _pickImageFromSource(ImageSource.camera);
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildImagePickerOption(
                      icon: Icons.photo_library,
                      label: AppLocalizations.of(context).gallery,
                      onTap: () {
                        Navigator.pop(context);
                        _pickImageFromSource(ImageSource.gallery);
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      );
    } catch (e) {
      debugPrint('Error showing image picker: $e');
    }
  }

  Widget _buildImagePickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F9FC),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: const Color(0xFF0D76FF),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuButton() {
    return GestureDetector(
      onTap: _showMenuBottomSheet,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: const Color(0xFF0D76FF),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Icon(
          Icons.more_vert,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }

  void _toggleWebSearchMode() {
    setState(() {
      _isWebSearchMode = !_isWebSearchMode;
    });

    // Update hint text and focus
    if (_isWebSearchMode) {
      _focusNode.requestFocus();
    }
  }

  Future<void> _performWebSearch(String query) async {
    if (query.trim().isEmpty) {
      // Show a message to enter search query
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Please enter a search query',
              style: GoogleFonts.instrumentSans(),
            ),
            backgroundColor: const Color(0xFF0D76FF),
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return;
    }

    // Check remaining searches
    final remainingSearches =
        await GoogleSearchService.getRemainingSearchCount();
    if (remainingSearches <= 0) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Daily search limit reached. You can perform 5 searches per day.',
              style: GoogleFonts.instrumentSans(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      // Create new session if none exists
      if (_currentChatSession == null) {
        await _createNewChatSession();
      }

      // Add user search message
      final userMessage = ChatMessage(
        text: query.trim(),
        isUser: true,
        timestamp: DateTime.now(),
        isWebSearchMessage: true,
      );

      setState(() {
        _messages.add(userMessage);
        _isTyping = true;
        _isGenerating = true;
        _shouldStopGeneration = false;

        // Trigger AppBar transition if this is the first message
        if (!_showTabBar) {
          _showTabBar = true;
        }
      });

      // Save user message to history
      await _saveMessageToHistory(userMessage);

      // Animate user message bubble
      _bubbleAnimationController.reset();
      _bubbleAnimationController.forward();

      // Clear the input and exit web search mode
      _messageController.clear();
      setState(() {
        _isWebSearchMode = false;
      });

      _scrollToBottom();

      // Start typing animation and show stop button
      _typingAnimationController.repeat();
      _stopButtonAnimationController.forward();

      // Create a completer for this generation
      _currentGenerationCompleter = Completer<void>();

      // Perform web search
      final searchResult = await GoogleSearchService.searchWeb(query.trim());

      // Generate AI response incorporating search results
      final aiPrompt = _buildSearchPrompt(query.trim(), searchResult);
      final aiResponse = await _getStreamedResponse(aiPrompt, [], 'detailed');

      // Create AI message with search results incorporated
      final aiMessage = ChatMessage(
        text: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
        searchResult: searchResult, // Keep for reference
        searchQuery: query,
      );

      setState(() {
        _messages.add(aiMessage);
        _isSearching = false;
        _isTyping = false;
        _isGenerating = false;
      });

      // Animate AI response bubble
      _bubbleAnimationController.reset();
      _bubbleAnimationController.forward();

      // Stop animations
      _typingAnimationController.stop();
      _stopButtonAnimationController.reverse();

      // Complete the generation
      _currentGenerationCompleter?.complete();

      // Save AI message to history
      await _saveMessageToHistory(aiMessage);

      // Update suggestions based on AI response
      _updateSuggestionsAfterAIResponse(aiMessage.text, userMessage: query);

      // Ensure scroll to bottom after UI update
      _scrollToBottom();

      // Additional scroll with delay to ensure proper positioning
      Future.delayed(const Duration(milliseconds: 100), () {
        _scrollToBottom();
      });
    } catch (error) {
      setState(() {
        _isSearching = false;
        _isTyping = false;
        _isGenerating = false;
      });

      // Stop animations
      _typingAnimationController.stop();
      _stopButtonAnimationController.reverse();

      // Complete the generation
      _currentGenerationCompleter?.complete();

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              error.toString(),
              style: GoogleFonts.instrumentSans(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Widget _buildWebSearchModeIndicator() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF0D76FF).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF0D76FF).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: const Color(0xFF0D76FF),
              borderRadius: BorderRadius.circular(6),
            ),
            child: _isSearching
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(
                    Icons.search,
                    color: Colors.white,
                    size: 16,
                  ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isSearching
                      ? 'Searching the Web...'
                      : 'Web Search Mode Active',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF0D76FF),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _isSearching
                      ? 'Please wait while I search for information'
                      : 'Your next message will search the web',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 12,
                    color: const Color(0xFF0D76FF).withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: _isSearching ? null : _toggleWebSearchMode,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF)
                    .withOpacity(_isSearching ? 0.05 : 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                Icons.close,
                color: Color(0xFF0D76FF).withOpacity(_isSearching ? 0.5 : 1.0),
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showMenuBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Menu options
            _buildMenuOption(
              icon: _isWebSearchMode ? Icons.search_off : Icons.search,
              title:
                  _isWebSearchMode ? 'Disable Web Search' : 'Enable Web Search',
              subtitle: _isWebSearchMode
                  ? 'Switch back to AI chat mode'
                  : 'Search the web for current information',
              onTap: () {
                Navigator.pop(context);
                _toggleWebSearchMode();
              },
            ),

            const Divider(height: 1),

            _buildMenuOption(
              icon: Icons.photo_library,
              title: AppLocalizations.of(context).pickImageFromGallery,
              subtitle: AppLocalizations.of(context).uploadImageForAiAnalysis,
              onTap: () {
                Navigator.pop(context);
                _pickImage();
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF0D76FF),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      color: const Color(0xFF718096),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _buildSearchPrompt(String query, GoogleSearchResult searchResult) {
    final searchResultsText = StringBuffer();

    if (searchResult.items.isNotEmpty) {
      searchResultsText
          .writeln('Based on the following web search results for "$query":');
      searchResultsText.writeln();

      for (int i = 0; i < searchResult.items.length; i++) {
        final item = searchResult.items[i];
        searchResultsText.writeln('${i + 1}. **${item.title}**');
        searchResultsText.writeln('   ${item.snippet}');
        searchResultsText.writeln('   Source: ${item.link}');
        searchResultsText.writeln();
      }

      searchResultsText.writeln(
          'Please provide a comprehensive answer to the query "$query" using the information from these search results. Include relevant details and cite the sources by referencing the links provided. Format your response in a helpful and informative way.');
    } else {
      searchResultsText.writeln(
          'I searched the web for "$query" but didn\'t find specific results. Please provide a general response about this topic based on your knowledge.');
    }

    return searchResultsText.toString();
  }

  Future<void> _pickImageFromSource(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        // Create new session if none exists
        if (_currentChatSession == null) {
          await _createNewChatSession();
        }

        // Create image message
        final imageMessage = ChatMessage(
          text:
              "Please analyze this image and tell me about what you see. If it's a location, identify landmarks, cities, or countries. For travel-related content like hotels, restaurants, attractions, or transportation, provide details and recommendations. Describe the scene and any travel suggestions you might have.",
          isUser: true,
          timestamp: DateTime.now(),
          imagePath: image.path,
          isImageMessage: true,
        );

        // Add image message to chat
        setState(() {
          _messages.add(imageMessage);

          // Trigger AppBar transition if this is the first message
          if (!_showTabBar) {
            _showTabBar = true;
          }
        });

        // Save image message to history
        await _saveMessageToHistory(imageMessage);

        // Animate user message bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();

        // Send image for analysis
        await _analyzeImage(image.path);
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _analyzeImage(String imagePath) async {
    setState(() {
      _isTyping = true;
      _isGenerating = true;
      _shouldStopGeneration = false;
    });

    // Start typing animation and show stop button
    _typingAnimationController.repeat();
    _stopButtonAnimationController.forward();

    // Create a completer for this generation
    _currentGenerationCompleter = Completer<void>();

    try {
      // Perform actual image analysis with the uploaded image
      final response = await _getStreamedImageAnalysisResponse(
        "I've uploaded an image. Please provide a detailed analysis focusing on: 1) Location identification (landmarks, cities, countries), 2) Travel-related content (hotels, restaurants, attractions, transportation), 3) Scene description and travel recommendations, 4) Cultural or historical significance if applicable, 5) Practical travel tips for this location or type of place.",
        [],
        imagePath,
      );

      if (mounted && !_shouldStopGeneration) {
        setState(() {
          _messages.add(
            ChatMessage(
              text: response,
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
          _isTyping = false;
          _isGenerating = false;
        });

        // Update suggestions based on AI response
        _updateSuggestionsAfterAIResponse(response,
            userMessage: "Analyze this image");

        // Animate AI response bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } catch (error) {
      if (mounted && !_shouldStopGeneration) {
        setState(() {
          _messages.add(
            ChatMessage(
              text:
                  "I can see you've shared an image, but I'm currently unable to analyze images directly. However, I'd be happy to help you with travel information if you can describe what you see in the image or tell me the location!",
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
          _isTyping = false;
          _isGenerating = false;
        });

        // Animate error message bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } finally {
      _typingAnimationController.stop();
      _stopButtonAnimationController.reverse();
      _currentGenerationCompleter = null;
    }
  }

  void _stopGeneration() {
    if (_isGenerating) {
      // Animate stop button out
      _stopButtonAnimationController.reverse();

      setState(() {
        _shouldStopGeneration = true;
        _isGenerating = false;
        _isTyping = false;
      });
      _typingAnimationController.stop();
      _currentGenerationCompleter?.complete();

      // Add a message indicating generation was stopped with animation
      setState(() {
        _messages.add(
          ChatMessage(
            text: "Response generation was stopped.",
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
      });

      // Animate new message bubble
      _bubbleAnimationController.reset();
      _bubbleAnimationController.forward();
      _scrollToBottom();
    }
  }

  Future<void> _sendMessage(String text) async {
    if (text.trim().isEmpty) return;

    // Check if we're in web search mode
    if (_isWebSearchMode) {
      await _performWebSearch(text);
      return;
    }

    // Create new session if none exists
    if (_currentChatSession == null) {
      await _createNewChatSession();
    }

    // Create user message
    final userMessage = ChatMessage(
      text: text,
      isUser: true,
      timestamp: DateTime.now(),
    );

    // Add user message with animation
    setState(() {
      _messages.add(userMessage);
      _isTyping = true;
      _isGenerating = true;
      _shouldStopGeneration = false;

      // Trigger AppBar transition if this is the first message
      if (!_showTabBar) {
        _showTabBar = true;
      }
    });

    // Save user message to history
    await _saveMessageToHistory(userMessage);

    // Animate user message bubble
    _bubbleAnimationController.reset();
    _bubbleAnimationController.forward();

    _messageController.clear();
    _scrollToBottom();

    // Start typing animation and show stop button
    _typingAnimationController.repeat();
    _stopButtonAnimationController.forward();

    // Create a completer for this generation
    _currentGenerationCompleter = Completer<void>();

    try {
      // Prepare conversation history for API
      final conversationHistory = _messages
          .where((msg) => msg.isUser || !msg.text.contains('Failed to'))
          .map((msg) => {
                'role': msg.isUser ? 'user' : 'assistant',
                'content': msg.text,
              })
          .toList();

      // Remove the current user message from history (it will be added by the service)
      if (conversationHistory.isNotEmpty) {
        conversationHistory.removeLast();
      }

      // Analyze question for appropriate response length
      final questionAnalysis = _analyzeQuestion(text);
      final responseLengthInstruction =
          questionAnalysis.responseLength == ResponseLength.short
              ? 'short'
              : 'long';

      // Get AI response with streaming simulation and contextual length
      final response = await _getStreamedResponse(
          text, conversationHistory, responseLengthInstruction);

      if (mounted && !_shouldStopGeneration) {
        final aiMessage = ChatMessage(
          text: response,
          isUser: false,
          timestamp: DateTime.now(),
        );

        setState(() {
          _messages.add(aiMessage);
          _isTyping = false;
          _isGenerating = false;
        });

        // Save AI message to history
        await _saveMessageToHistory(aiMessage);

        // Update suggestions based on AI response
        _updateSuggestionsAfterAIResponse(aiMessage.text, userMessage: text);

        // Animate AI response bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } catch (error) {
      if (mounted && !_shouldStopGeneration) {
        final errorMessage = ChatMessage(
          text: GeminiService.getErrorMessage(error),
          isUser: false,
          timestamp: DateTime.now(),
        );

        setState(() {
          _messages.add(errorMessage);
          _isTyping = false;
          _isGenerating = false;
        });

        // Save error message to history
        await _saveMessageToHistory(errorMessage);

        // Animate error message bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } finally {
      _typingAnimationController.stop();
      _stopButtonAnimationController.reverse();
      _currentGenerationCompleter = null;
    }
  }

  Future<String> _getStreamedImageAnalysisResponse(String message,
      List<Map<String, String>> conversationHistory, String imagePath) async {
    // Simulate streaming by adding delays and checking for stop conditions
    const int checkIntervalMs = 100;
    const int totalDelayMs = 2000; // Longer delay for image analysis
    int elapsedMs = 0;

    while (elapsedMs < totalDelayMs && !_shouldStopGeneration) {
      await Future.delayed(const Duration(milliseconds: checkIntervalMs));
      elapsedMs += checkIntervalMs;
    }

    // If we made it here without being stopped, get the actual response using Gemini
    if (!_shouldStopGeneration) {
      return await GeminiService.analyzeImage(imagePath, message);
    } else {
      throw Exception('Generation stopped by user');
    }
  }

  Future<String> _getStreamedResponse(
      String message,
      List<Map<String, String>> conversationHistory,
      String responseLength) async {
    // Simulate streaming by adding delays and checking for stop conditions
    const int checkIntervalMs = 100;
    const int totalDelayMs = 1500; // Simulate 1.5 seconds of "generation"
    int elapsedMs = 0;

    while (elapsedMs < totalDelayMs && !_shouldStopGeneration) {
      await Future.delayed(const Duration(milliseconds: checkIntervalMs));
      elapsedMs += checkIntervalMs;

      // Check if generation should be stopped
      if (_shouldStopGeneration) {
        throw Exception('Generation stopped by user');
      }
    }

    // If we made it here without being stopped, get the actual response
    if (!_shouldStopGeneration) {
      return await GeminiService.sendMessage(message, conversationHistory,
          responseLength: responseLength);
    } else {
      throw Exception('Generation stopped by user');
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      body: Stack(
        children: [
          // Main chat interface
          FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Top navigation (replaces AppBar)
                _buildTopNavigation(),

                // Chat messages area
                Expanded(
                  child: _buildChatContent(),
                ),

                // Stop button area (above chat input)
                _buildStopButtonArea(),

                // Chat input area
                _buildChatInput(),
              ],
            ),
          ),

          // Chat history sidebar
          ChatHistorySidebar(
            isVisible: _isSidebarVisible,
            onClose: () => setState(() => _isSidebarVisible = false),
            onChatSelected: _onChatSelected,
            onNewChat: _onNewChat,
          ),
        ],
      ),
    );
  }

  Widget _buildChatContent() {
    // Show loading indicator while history is being initialized
    if (!_isHistoryInitialized) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
        ),
      );
    }

    // Use TabBarView if TabBar is visible, otherwise show chat content directly
    if (_showTabBar) {
      return TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          // Chat Tab
          _buildChatTabContent(),
          // Plan Tab
          _buildPlanTabContent(),
        ],
      );
    } else {
      // Show initial state when no TabBar is visible
      return _buildInitialState();
    }
  }

  Widget _buildChatTabContent() {
    // After history is initialized, check if we have messages to display
    if (_hasUserMessages()) {
      return _buildFlexibleChatLayout();
    } else {
      return _buildInitialState();
    }
  }

  Widget _buildPlanTabContent() {
    if (_isLoadingEnhancedContent) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
        ),
      );
    }

    if (_enhancedContent.isEmpty) {
      return _buildEmptyPlanState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'AI-Generated Content',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 16),

          // Enhanced content items
          ..._enhancedContent.map(
            (contentItem) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildContentWidget(contentItem),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyPlanState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No AI Content Yet',
            style: GoogleFonts.instrumentSans(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start chatting to generate\nitineraries, tables, and travel guides!',
            textAlign: TextAlign.center,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              height: 1.5,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedTabIndex = 0; // Switch to Chat tab
              });
              _tabController.animateTo(0);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0D76FF),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Start Chatting',
              style: GoogleFonts.instrumentSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentWidget(EnhancedContentItem contentItem) {
    // Use interactive widget for itinerary content, regular display for others
    if (contentItem.content.type == ContentType.itinerary &&
        contentItem.content.itinerary != null) {
      return InteractiveItineraryWidget(
        content: contentItem.content,
        onSave: () {
          // Refresh enhanced content after saving
          _loadEnhancedContent();
        },
      );
    } else {
      return EnhancedContentDisplay(
        content: contentItem.content,
        onTap: () => _showEnhancedContentDetail(contentItem),
      );
    }
  }

  void _showEnhancedContentDetail(EnhancedContentItem contentItem) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxHeight: 600),
          child: EnhancedContentDisplay(
            content: contentItem.content,
            isCompact: false,
          ),
        ),
      ),
    );
  }

  /// Navigate to the Plan tab within the chat screen
  void _navigateToPlanTab() {
    setState(() {
      _selectedTabIndex = 1; // Plan tab index
    });
    _tabController.animateTo(1);

    // Load enhanced content when navigating to plan tab
    _loadEnhancedContent();
  }

  Future<void> _loadEnhancedContent() async {
    setState(() {
      _isLoadingEnhancedContent = true;
    });

    try {
      final content = await EnhancedContentService.getAllEnhancedContent();
      if (mounted) {
        setState(() {
          _enhancedContent = content;
          _isLoadingEnhancedContent = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingEnhancedContent = false;
        });
      }
      if (kDebugMode) {
        print('Error loading enhanced content: $e');
      }
    }
  }

  Widget _buildInitialState() {
    return AnimatedBuilder(
      animation: _textAnimationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _textFadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height * 0.5,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Initial AI greeting bubble with animation
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 800),
                    tween: Tween(begin: 0.0, end: 1.0),
                    curve: Curves.elasticOut,
                    builder: (context, value, child) {
                      return Transform.scale(
                        scale: value.clamp(0.0, 1.0),
                        child: Opacity(
                          opacity: value.clamp(0.0, 1.0),
                          child: _buildInitialGreetingBubble(),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Message length categories for layout decisions
  MessageSize _getMessageSize(ChatMessage message) {
    final textLength = message.text.length;
    final hasImage = message.imagePath != null;

    // Images always take full width
    if (hasImage) return MessageSize.large;

    // Text length thresholds
    if (textLength <= 50) return MessageSize.small;
    if (textLength <= 150) return MessageSize.medium;
    return MessageSize.large;
  }

  Widget _buildFlexibleChatLayout() {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          _buildMasonryLayout(),
          if (_isTyping) _buildTypingIndicator(),
        ],
      ),
    );
  }

  Widget _buildMasonryLayout() {
    if (_messages.isEmpty) return const SizedBox.shrink();

    List<Widget> rows = [];
    int currentIndex = 0;

    while (currentIndex < _messages.length) {
      final rowData = _buildMessageRow(currentIndex);
      rows.add(rowData.widget);
      currentIndex = rowData.nextIndex;
    }

    return Column(
      children: rows,
    );
  }

  MessageRowData _buildMessageRow(int startIndex) {
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - 32; // Account for horizontal padding

    List<ChatMessage> rowMessages = [];
    List<Widget> rowWidgets = [];
    double usedWidth = 0;
    int currentIndex = startIndex;

    while (currentIndex < _messages.length) {
      final message = _messages[currentIndex];
      final messageSize = _getMessageSize(message);
      final estimatedWidth = _getEstimatedBubbleWidth(message, availableWidth);

      // Check if this message can fit in the current row
      bool canFitInRow = rowMessages.isEmpty ||
          (usedWidth + estimatedWidth <= availableWidth &&
              messageSize != MessageSize.large &&
              rowMessages.length < 2);

      // Additional constraint: don't mix user and AI messages in same row for clarity
      if (rowMessages.isNotEmpty &&
          rowMessages.first.isUser != message.isUser) {
        canFitInRow = false;
      }

      if (canFitInRow) {
        rowMessages.add(message);
        usedWidth += estimatedWidth;
        currentIndex++;

        // Large messages always take full row
        if (messageSize == MessageSize.large) break;
      } else {
        break;
      }
    }

    // Build the row widget
    if (rowMessages.length == 1) {
      // Single message in row
      rowWidgets.add(_buildMessageBubble(rowMessages[0]));
    } else {
      // Multiple messages in row - arrange side by side
      rowWidgets.add(_buildMultiMessageRow(rowMessages, availableWidth));
    }

    return MessageRowData(
      widget: Column(children: rowWidgets),
      nextIndex: currentIndex,
    );
  }

  Widget _buildMultiMessageRow(
      List<ChatMessage> messages, double availableWidth) {
    // Determine alignment based on message type
    final isUserRow = messages.first.isUser;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            isUserRow ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: messages.map((message) {
          final isLast = message == messages.last;
          return Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: availableWidth * 0.45, // Max 45% width per bubble
              ),
              margin: EdgeInsets.only(
                left: isUserRow && !isLast ? 8 : 0,
                right: !isUserRow && !isLast ? 8 : 0,
              ),
              child: _buildCompactMessageBubble(message),
            ),
          );
        }).toList(),
      ),
    );
  }

  double _getEstimatedBubbleWidth(ChatMessage message, double maxWidth) {
    final messageSize = _getMessageSize(message);

    switch (messageSize) {
      case MessageSize.small:
        return maxWidth * 0.4; // 40% of available width
      case MessageSize.medium:
        return maxWidth * 0.6; // 60% of available width
      case MessageSize.large:
        return maxWidth; // Full width
    }
  }

  Widget _buildCompactMessageBubble(ChatMessage message) {
    return AnimatedBuilder(
      animation: _bubbleAnimationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _bubbleFadeAnimation,
          child: ScaleTransition(
            scale: _bubbleScaleAnimation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.3),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: _bubbleAnimationController,
                curve: Curves.easeInOut,
              )),
              child: GestureDetector(
                onTap: () => _showExpandedMessage(message),
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: message.isUser ? 10 : 12,
                  ),
                  decoration: BoxDecoration(
                    color:
                        message.isUser ? const Color(0xFF0D76FF) : Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(16),
                      topRight: const Radius.circular(16),
                      bottomLeft: message.isUser
                          ? const Radius.circular(16)
                          : const Radius.circular(4),
                      bottomRight: message.isUser
                          ? const Radius.circular(4)
                          : const Radius.circular(16),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      message.isUser
                          ? _buildCompactUserMessageContent(message)
                          : _buildCompactAIMessageContent(message),
                      // Add expand indicator for truncated messages
                      if (_isMessageTruncated(message))
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: message.isUser
                                  ? Colors.white.withOpacity(0.2)
                                  : const Color(0xFF0D76FF).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.expand_more,
                              size: 12,
                              color: message.isUser
                                  ? Colors.white.withOpacity(0.8)
                                  : const Color(0xFF0D76FF).withOpacity(0.6),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  bool _isMessageTruncated(ChatMessage message) {
    // Check if message would be truncated in compact view
    return message.text.length > 100 || message.text.split('\n').length > 3;
  }

  void _showExpandedMessage(ChatMessage message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: message.isUser ? const Color(0xFF0D76FF) : Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      message.isUser ? 'Your Message' : 'Wanderly AI',
                      style: GoogleFonts.instrumentSans(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: message.isUser
                            ? Colors.white
                            : const Color(0xFF2D3748),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.close,
                        color: message.isUser
                            ? Colors.white
                            : const Color(0xFF718096),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Message content
                Flexible(
                  child: SingleChildScrollView(
                    child: message.isUser
                        ? _buildUserMessageContent(message)
                        : _buildAIMessageContent(message),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompactUserMessageContent(ChatMessage message) {
    return Text(
      message.text,
      style: GoogleFonts.instrumentSans(
        fontSize: 13,
        color: Colors.white,
        height: 1.4,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildCompactAIMessageContent(ChatMessage message) {
    // For compact layout, use TravelEnhancedMarkdown but with compact styling
    return TravelEnhancedMarkdown(
      data: message.text,
      isUserMessage: false,
      isCompact: true, // Add compact mode parameter
    );
  }

  Widget _buildInitialGreetingBubble() {
    return Column(
      children: [
        // AI Greeting Message - as normal title text
        Column(
          children: [
            Text(
              AppLocalizations.of(context).hiImWanderlyAi,
              style: GoogleFonts.instrumentSans(
                fontSize: 24,
                color: const Color(0xFF2D3748),
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              AppLocalizations.of(context).yourTravelAiAssistant,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                color: const Color(0xFF718096),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        const SizedBox(height: 20),
        // Suggestions Container
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Text(
                AppLocalizations.of(context).useThisBubbleChat,
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  color: const Color(0xFF718096),
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              _buildSuggestions()
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return AnimatedBuilder(
      animation: _bubbleAnimationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _bubbleFadeAnimation,
          child: ScaleTransition(
            scale: _bubbleScaleAnimation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.3),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: _bubbleAnimationController,
                curve: Curves.easeInOut,
              )),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                child: Row(
                  mainAxisAlignment: message.isUser
                      ? MainAxisAlignment.end
                      : MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Container(
                        constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width * 0.8,
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: message.isUser ? 14 : 16,
                        ),
                        decoration: BoxDecoration(
                          color: message.isUser
                              ? const Color(0xFF0D76FF)
                              : Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: const Radius.circular(20),
                            topRight: const Radius.circular(20),
                            bottomLeft: message.isUser
                                ? const Radius.circular(20)
                                : const Radius.circular(4),
                            bottomRight: message.isUser
                                ? const Radius.circular(4)
                                : const Radius.circular(20),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: message.isUser
                            ? _buildUserMessageContent(message)
                            : _buildAIMessageContent(message),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserMessageContent(ChatMessage message) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display image if present
        if (message.imagePath != null) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              File(message.imagePath!),
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 8),
        ],
        // Display search indicator for web search messages
        if (message.isWebSearchMessage) ...[
          Row(
            children: [
              Icon(
                Icons.search,
                size: 16,
                color: Colors.white.withOpacity(0.8),
              ),
              const SizedBox(width: 6),
              Text(
                'Web Search:',
                style: GoogleFonts.instrumentSans(
                  fontSize: 12,
                  color: Colors.white.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
        ],
        // Display text
        Text(
          message.text,
          style: GoogleFonts.instrumentSans(
            fontSize: 14,
            color: Colors.white,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildAIMessageContent(ChatMessage message) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Check if this is a web search result message
        if (message.searchResult != null && message.searchQuery != null) ...[
          // Web search results
          WebSearchResults(
            searchResult: message.searchResult!,
            searchQuery: message.searchQuery!,
            isUserMessage: false,
          ),
        ] else ...[
          // Regular AI response content
          TravelEnhancedMarkdown(
            data: message.text,
            isUserMessage: false,
          ),
        ],
        const SizedBox(height: 12),
        // Action buttons (only show for regular AI messages, not search results)
        if (message.searchResult == null) ...[
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Feedback buttons
              _buildFeedbackButton(
                icon: Icons.thumb_up_outlined,
                label: 'Like',
                isSelected: _getMessageFeedback(message) == 'like',
                onTap: () => _handleFeedback(message, FeedbackType.like),
              ),
              const SizedBox(width: 8),
              _buildFeedbackButton(
                icon: Icons.thumb_down_outlined,
                label: 'Dislike',
                isSelected: _getMessageFeedback(message) == 'dislike',
                onTap: () => _handleFeedback(message, FeedbackType.dislike),
              ),
              const SizedBox(width: 8),
              _buildActionButton(
                icon: Icons.copy,
                label: 'Copy',
                onTap: () => _copyToClipboard(message.text),
              ),
              const SizedBox(width: 8),
              _buildActionButton(
                icon: Icons.refresh,
                label: 'Regenerate',
                onTap: () => _regenerateResponse(message),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: const Color(0xFFF7F9FC),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: Icon(
          icon,
          size: 16,
          color: const Color(0xFF64748B),
        ),
      ),
    );
  }

  Widget _buildFeedbackButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF0D76FF).withOpacity(0.1)
              : const Color(0xFFF7F9FC),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF0D76FF) : const Color(0xFFE2E8F0),
          ),
        ),
        child: Icon(
          icon,
          size: 16,
          color: isSelected ? const Color(0xFF0D76FF) : const Color(0xFF64748B),
        ),
      ),
    );
  }

  /// Get current feedback for a message
  String? _getMessageFeedback(ChatMessage message) {
    // For now, return null since we need to implement the mapping
    // between ChatMessage and ChatMessageModel
    return null;
  }

  /// Handle feedback submission
  Future<void> _handleFeedback(
      ChatMessage message, FeedbackType feedbackType) async {
    try {
      if (feedbackType == FeedbackType.dislike) {
        // Show feedback dialog for dislike
        await _showFeedbackDialog(message);
      } else {
        // Submit like feedback directly
        await _submitFeedback(message, feedbackType, null, null);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error handling feedback: $e');
      }
      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to submit feedback. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Show feedback dialog for dislike
  Future<void> _showFeedbackDialog(ChatMessage message) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return FeedbackDialog(
          onSubmit: (reason, customText) async {
            Navigator.of(context).pop();
            await _submitFeedback(
                message, FeedbackType.dislike, reason, customText);
          },
          onCancel: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// Submit feedback to the service
  Future<void> _submitFeedback(
    ChatMessage message,
    FeedbackType feedbackType,
    FeedbackReason? reason,
    String? customText,
  ) async {
    try {
      // Find the user message that preceded this AI message
      final aiIndex = _messages.indexOf(message);
      String userMessage = '';

      if (aiIndex > 0) {
        final precedingMessage = _messages[aiIndex - 1];
        if (precedingMessage.isUser) {
          userMessage = precedingMessage.text;
        }
      }

      // Get conversation context (last 5 messages)
      final contextMessages = _messages
          .take(aiIndex + 1)
          .map((msg) => '${msg.isUser ? "User" : "AI"}: ${msg.text}')
          .toList();

      // Submit feedback
      final success = await FeedbackService.submitFeedback(
        userMessage: userMessage,
        aiResponse: message.text,
        feedbackType: feedbackType,
        feedbackReason: reason,
        customFeedbackText: customText,
        conversationContext: contextMessages,
        sessionId: _currentChatSession?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        messageId: DateTime.now()
            .millisecondsSinceEpoch
            .toString(), // Generate unique ID
      );

      if (success && mounted) {
        // Show simple success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            behavior: SnackBarBehavior.floating,
            content: Text(
              feedbackType == FeedbackType.like
                  ? 'Thank you for your feedback! 🙏'
                  : 'Feedback received. Thank you for helping us improve! 🚀',
              style: GoogleFonts.instrumentSans(fontWeight: FontWeight.w600),
            ),
            backgroundColor: const Color(0xFF0D76FF),
            duration: const Duration(seconds: 2),
          ),
        );

        // Update UI to reflect feedback
        setState(() {
          // Force rebuild to update button states
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error submitting feedback: $e');
      }
      rethrow;
    }
  }

  void _copyToClipboard(String text) {
    // Remove markdown formatting and image markers for clean copy
    String cleanText = text.replaceAll(RegExp(r'\[TRAVEL_IMAGES:.*?\]'), '');
    cleanText = cleanText.replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1'); // Bold
    cleanText = cleanText.replaceAll(RegExp(r'\*(.*?)\*'), r'$1'); // Italic
    cleanText = cleanText.replaceAll(RegExp(r'`(.*?)`'), r'$1'); // Code
    cleanText = cleanText.trim();

    Clipboard.setData(ClipboardData(text: cleanText));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Response copied to clipboard',
          style: GoogleFonts.instrumentSans(),
        ),
        backgroundColor: const Color(0xFF0D76FF),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _regenerateResponse(ChatMessage aiMessage) {
    // Find the user message that preceded this AI message
    final aiIndex = _messages.indexOf(aiMessage);
    if (aiIndex > 0) {
      final userMessage = _messages[aiIndex - 1];
      if (userMessage.isUser) {
        // Remove the AI message and regenerate
        setState(() {
          _messages.removeAt(aiIndex);
        });

        // Regenerate AI response without duplicating user message
        if (userMessage.isImageMessage && userMessage.imagePath != null) {
          _regenerateImageAnalysis(userMessage);
        } else {
          _regenerateTextResponse(userMessage);
        }
      }
    }
  }

  /// Regenerate AI response for text messages without duplicating user message
  Future<void> _regenerateTextResponse(ChatMessage userMessage) async {
    setState(() {
      _isTyping = true;
      _isGenerating = true;
      _shouldStopGeneration = false;
    });

    // Start typing animation and show stop button
    _typingAnimationController.repeat();
    _stopButtonAnimationController.forward();

    // Create a completer for this generation
    _currentGenerationCompleter = Completer<void>();

    try {
      // Prepare conversation history for API (excluding the current user message)
      final conversationHistory = _messages
          .where((msg) => msg.isUser || !msg.text.contains('Failed to'))
          .map((msg) => {
                'role': msg.isUser ? 'user' : 'assistant',
                'content': msg.text,
              })
          .toList();

      // Analyze question for appropriate response length
      final questionAnalysis = _analyzeQuestion(userMessage.text);
      final responseLengthInstruction =
          questionAnalysis.responseLength == ResponseLength.short
              ? 'short'
              : 'long';

      // Get AI response with streaming simulation and contextual length
      final response = await _getStreamedResponse(
          userMessage.text, conversationHistory, responseLengthInstruction);

      if (mounted && !_shouldStopGeneration) {
        final aiMessage = ChatMessage(
          text: response,
          isUser: false,
          timestamp: DateTime.now(),
        );

        setState(() {
          _messages.add(aiMessage);
          _isTyping = false;
          _isGenerating = false;
        });

        // Save AI message to history
        await _saveMessageToHistory(aiMessage);

        // Update suggestions based on AI response
        _updateSuggestionsAfterAIResponse(aiMessage.text,
            userMessage: userMessage.text);

        // Animate AI response bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } catch (error) {
      if (mounted && !_shouldStopGeneration) {
        final errorMessage = ChatMessage(
          text: GeminiService.getErrorMessage(error),
          isUser: false,
          timestamp: DateTime.now(),
        );

        setState(() {
          _messages.add(errorMessage);
          _isTyping = false;
          _isGenerating = false;
        });

        // Save error message to history
        await _saveMessageToHistory(errorMessage);

        // Animate error message bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } finally {
      _typingAnimationController.stop();
      _stopButtonAnimationController.reverse();
      _currentGenerationCompleter = null;
    }
  }

  /// Regenerate AI response for image messages without duplicating user message
  Future<void> _regenerateImageAnalysis(ChatMessage userMessage) async {
    setState(() {
      _isTyping = true;
      _isGenerating = true;
      _shouldStopGeneration = false;
    });

    // Start typing animation and show stop button
    _typingAnimationController.repeat();
    _stopButtonAnimationController.forward();

    // Create a completer for this generation
    _currentGenerationCompleter = Completer<void>();

    try {
      // Perform actual image analysis with the uploaded image
      final response = await _getStreamedImageAnalysisResponse(
        "I've uploaded an image. Please provide a detailed analysis focusing on: 1) Location identification (landmarks, cities, countries), 2) Travel-related content (hotels, restaurants, attractions, transportation), 3) Scene description and travel recommendations, 4) Cultural or historical significance if applicable, 5) Practical travel tips for this location or type of place.",
        [],
        userMessage.imagePath!,
      );

      if (mounted && !_shouldStopGeneration) {
        final aiMessage = ChatMessage(
          text: response,
          isUser: false,
          timestamp: DateTime.now(),
        );

        setState(() {
          _messages.add(aiMessage);
          _isTyping = false;
          _isGenerating = false;
        });

        // Save AI message to history
        await _saveMessageToHistory(aiMessage);

        // Update suggestions based on AI response
        _updateSuggestionsAfterAIResponse(response,
            userMessage: userMessage.text);

        // Animate AI response bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } catch (error) {
      if (mounted && !_shouldStopGeneration) {
        setState(() {
          _messages.add(
            ChatMessage(
              text: 'Failed to analyze image. Please try again.',
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
          _isTyping = false;
          _isGenerating = false;
        });

        // Animate error message bubble
        _bubbleAnimationController.reset();
        _bubbleAnimationController.forward();
        _scrollToBottom();
      }
    } finally {
      _typingAnimationController.stop();
      _stopButtonAnimationController.reverse();
      _currentGenerationCompleter = null;
    }
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Row(
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Wanderly AI is typing',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 13,
                    color: const Color(0xFF718096),
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(width: 8),
                AnimatedBuilder(
                  animation: _typingAnimation,
                  builder: (context, child) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        for (int i = 0; i < 3; i++) ...[
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: Color.lerp(
                                const Color(0xFF0D76FF).withOpacity(0.3),
                                const Color(0xFF0D76FF),
                                ((math.sin((_typingAnimation.value
                                                        .clamp(0.0, 1.0) *
                                                    2 *
                                                    math.pi) +
                                                (i * 0.5)) +
                                            1) /
                                        2)
                                    .clamp(0.0, 1.0),
                              ),
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                          if (i < 2) const SizedBox(width: 4),
                        ],
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestions() {
    return AnimatedBuilder(
      animation: _textAnimationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _textFadeAnimation,
          child: ScaleTransition(
            scale: _textScaleAnimation,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _suggestions.asMap().entries.map((entry) {
                      final index = entry.key;
                      final suggestion = entry.value;
                      return TweenAnimationBuilder<double>(
                        duration: Duration(milliseconds: 300 + (index * 100)),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: value.clamp(0.0, 1.0),
                            child: Opacity(
                              opacity: value.clamp(0.0, 1.0),
                              child: GestureDetector(
                                onTap: () async =>
                                    await _sendMessage(suggestion),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 10),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: const Color(0xFFE2E8F0),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    suggestion,
                                    style: GoogleFonts.instrumentSans(
                                      fontSize: 13,
                                      color: const Color(0xFF2D3748),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStopButtonArea() {
    return AnimatedBuilder(
      animation: _stopButtonAnimationController,
      builder: (context, child) {
        return SlideTransition(
          position: _stopButtonSlideAnimation,
          child: FadeTransition(
            opacity: _stopButtonFadeAnimation,
            child: _isGenerating
                ? Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _stopGeneration,
                          icon: const Icon(Icons.stop, size: 16),
                          label: Text(
                            'Stop Generation',
                            style: GoogleFonts.instrumentSans(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFFFF4757),
                            foregroundColor: Colors.white,
                            elevation: 2,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        );
      },
    );
  }

  Widget _buildChatInput() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 400),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 20),
              decoration: const BoxDecoration(
                color: Color(0xFFF7F9FC),
              ),
              child: Column(
                children: [
                  // Chat limit indicator with model selector
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.flash_on,
                              size: 16,
                              color: const Color(0xFF0D76FF),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'You have 10 chats left',
                              style: GoogleFonts.instrumentSans(
                                fontSize: 12,
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      // Model selector positioned on the right
                      ModelSelector(
                        isCompact: true,
                        onModelChanged: (model) {
                          // Optional: Show a brief feedback to user
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Switched to ${GeminiService.isPremiumModelSelected() ? 'Premium' : 'Default'} model',
                                  style: GoogleFonts.instrumentSans(),
                                ),
                                behavior: SnackBarBehavior.floating,
                                duration: const Duration(seconds: 2),
                                backgroundColor: const Color(0xFF0D76FF),
                              ),
                            );
                          }
                        },
                      ),
                      const SizedBox(
                          width: 12), // Add some padding from the right edge
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Web search mode indicator
                  if (_isWebSearchMode) ...[
                    _buildWebSearchModeIndicator(),
                    const SizedBox(height: 8),
                  ],

                  // Input field
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15)),
                    ),
                    child: Row(
                      children: [
                        // Menu button
                        _buildMenuButton(),
                        const SizedBox(width: 8),

                        // Text input
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: _isWebSearchMode
                                  ? Border.all(
                                      color: const Color(0xFF0D76FF),
                                      width: 1.5,
                                    )
                                  : null,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: TextField(
                              controller: _messageController,
                              focusNode: _focusNode,
                              decoration: InputDecoration(
                                hintText: _isListening
                                    ? AppLocalizations.of(context).listening
                                    : _isWebSearchMode
                                        ? AppLocalizations.of(context)
                                            .enterSearchQuery
                                        : AppLocalizations.of(context)
                                            .askMeAnythingOrLongPress,
                                hintStyle: GoogleFonts.instrumentSans(
                                  fontSize: 14,
                                  color: _isListening
                                      ? const Color(0xFF0D76FF)
                                      : _isWebSearchMode
                                          ? const Color(0xFF0D76FF)
                                          : const Color(0xFF718096),
                                  fontStyle: _isListening || _isWebSearchMode
                                      ? FontStyle.italic
                                      : FontStyle.normal,
                                ),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              style: GoogleFonts.instrumentSans(
                                fontSize: 14,
                                color: _isListening
                                    ? const Color(0xFF0D76FF)
                                    : _isWebSearchMode
                                        ? const Color(0xFF0D76FF)
                                        : const Color(0xFF2D3748),
                                fontStyle: _isListening || _isWebSearchMode
                                    ? FontStyle.italic
                                    : FontStyle.normal,
                              ),
                              onSubmitted: (text) async =>
                                  await _sendMessage(text),
                              textInputAction: TextInputAction.send,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),

                        // Send/Voice button
                        _buildVoiceEnabledSendButton(),
                      ],
                    ),
                  ),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200.withOpacity(0.5),
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(15),
                            bottomRight: Radius.circular(15)),
                      ),
                      child: Row(
                        children: _bottomSuggestions
                            .asMap()
                            .entries
                            .map((entry) {
                              final index = entry.key;
                              final suggestion = entry.value;
                              return [
                                if (index > 0) const SizedBox(width: 8),
                                _buildBottomSuggestion(suggestion),
                              ];
                            })
                            .expand((widgets) => widgets)
                            .toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomSuggestion(String text) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 500),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value.clamp(0.0, 1.0),
          child: Opacity(
            opacity: value.clamp(0.0, 1.0),
            child: GestureDetector(
              onTap: () async => await _sendMessage(text),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFE2E8F0),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Text(
                  text,
                  style: GoogleFonts.instrumentSans(
                    fontSize: 12,
                    color: const Color(0xFF718096),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildVoiceEnabledSendButton() {
    return AnimatedBuilder(
      animation: _voiceButtonAnimationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _voiceButtonScaleAnimation.value,
          child: GestureDetector(
            onTap: () async {
              // Regular tap - send text message
              if (_messageController.text.trim().isNotEmpty) {
                await _sendMessage(_messageController.text);
              }
            },
            onLongPressStart: (details) async {
              // Long press start - begin voice recording
              HapticFeedback.mediumImpact();
              await _startListening();
            },
            onLongPressEnd: (details) async {
              // Long press end - stop voice recording
              await _stopListening();
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _isListening
                    ? const Color(0xFF0D76FF).withOpacity(0.8)
                    : const Color(0xFF0D76FF),
                shape: BoxShape.circle,
                boxShadow: _isListening
                    ? [
                        BoxShadow(
                          color: const Color(0xFF0D76FF).withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ]
                    : null,
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Pulsing animation when recording
                  if (_isListening)
                    AnimatedBuilder(
                      animation: _voiceRecordingAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: 1.0 + (_voiceRecordingAnimation.value * 0.3),
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: const Color(0xFF0D76FF).withOpacity(
                                (0.2 *
                                        (1 -
                                            _voiceRecordingAnimation.value
                                                .clamp(0.0, 1.0)))
                                    .clamp(0.0, 1.0),
                              ),
                              shape: BoxShape.circle,
                            ),
                          ),
                        );
                      },
                    ),
                  // Icon that morphs between send and microphone
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder: (child, animation) {
                      return ScaleTransition(
                        scale: animation,
                        child: child,
                      );
                    },
                    child: Icon(
                      _isListening ? Icons.mic : Icons.send,
                      key: ValueKey(_isListening),
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? imagePath; // For uploaded images
  final bool isImageMessage; // Whether this is primarily an image message
  final bool isWebSearchMessage; // Whether this is a web search message
  final GoogleSearchResult? searchResult; // Web search results
  final String? searchQuery; // Original search query

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.imagePath,
    this.isImageMessage = false,
    this.isWebSearchMessage = false,
    this.searchResult,
    this.searchQuery,
  });
}
